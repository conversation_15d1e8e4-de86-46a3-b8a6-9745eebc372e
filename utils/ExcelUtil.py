import os
import xlrd

# 目的：参数化，我们在遍历判断规则数据时 数据需要是list格式，所以我们封装这个结果以list来返回结果数据
# 自定义异常
class sheetTypeError:
    pass
# 验证文件是否存在，存在读取 不存在报错
class ExcelReader:
    def __init__(self,excel_file,sheet_by):
        # 验证文件是否存在，存在读取 不存在报错
        if os.path.exists(excel_file):
            self.excel_file = excel_file
            self.sheet_by = sheet_by
            # 表格里所有数据（不包括表头）
            self._data = list()
            # 所有联赛
            self._League_data = list()
            # 所有的联赛结束场
            self._Ending_number = list()
            # 所有洲际杯赛
            self._Club_Intercontinental_Cup = list()
            # 所有国家队比赛
            self._Country_games = list()
        else:
            raise FileExistsError("文件不存在")
    # 读取sheet 方式 判断是 用名称还是索引
    def data(self):
        #如果存在数据 不读取，不存在去读取
        if not self._data:
            workbook = xlrd.open_workbook(self.excel_file)
            # 无论是用名称还是索引读取sheet， self.sheet_by无非就是str,int 类型数据，所以在此加上限制类型
            if type(self.sheet_by) not in [str,int]:
                raise sheetTypeError("请输入int or str")
            elif type(self.sheet_by) == int:
                sheet = workbook.sheet_by_index(self.sheet_by)
            elif type(self.sheet_by) == str:
                sheet = workbook.sheet_by_name(self.sheet_by)
        #读取sheet 内容
        #返回list,元素是字典格式
        #格式[{"a":"a1","b":"b1"},{"a":"a2","b":"b2"}]，首行为key,下面每一行每月一个单元格为一个value
            title = sheet.row_values(0)
            # print(title)
        # 遍历测试行，与首行组成dict,放在list
         #循环，过滤首行，从1开始
            for col in range(1,sheet.nrows):
                col_value = sheet.row_values(col)
                # 与首行组成字典
                self._data.append(dict(zip(title,col_value)))
            # for result in self._data:
            #     self._League_data.append(result[title[0]])
            # for result5 in self._data:
            #     self._Club_Intercontinental_Cup.append(result5[title[5]])
            # for result6 in self._data:
            #     self._Country_games.append(result6[title[6]])
        # print(self._Club_Intercontinental_Cup,self._Country_games)
        # return self._data,self._League_data,self._Club_Intercontinental_Cup,self._Country_games
        # print(self._data)
        return self._data


    # def League_data(self):
    #     # print(self._League_data)
    #     return



if __name__ == '__main__':

    reader = ExcelReader("../data/LeagueOverview.xlsx","PushRules")
    reader.data()
    # reader.League_data()







