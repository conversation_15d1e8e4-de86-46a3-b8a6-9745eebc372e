import sys
sys.path.append(r"C:\Jenkins_workspaces\workspace\RecommendedSystem")
import time
from dateutil.relativedelta import relativedelta
from utils.DriverUtil import Driver_data
from config import Conf
from lxml import etree
from bs4 import BeautifulSoup
import re
import datetime
from datetime import datetime as date_
class Details_Rule:
    def __init__(self,driver_path,Details_url):
        self.driver_path = driver_path
        self.Details_url = Details_url
        # 联赛名称
        self.League_name = ""
        #详细联赛名称
        self.detail_League_name = ""
        # 当前比赛是联赛的第几轮
        self.home_Session = int()
        # 主队数据
        self.Home_team_data = dict()
        # 客队数据
        self.Away_team_data = dict()
        #     往期对战中的客队名称
        self.past_games_Away_name = {"past_games_Away_name": ""}
        #     往期对战时间
        self.battleTime = False
    def Three_points(self):
        try:
            data = Driver_data(self.driver_path).Details_driver(self.Details_url)
        except Exception as e:
            print("第一次加载网页数据报异常了")
            time.sleep(6)
            data = Driver_data(self.driver_path).Details_driver(self.Details_url)
            print("重试后加载到网页数据了")

        # driver = webdriver.Chrome(executable_path="D:\software\chromedriver\chromedriver_win32\chromedriver.exe")
        # url = "https://m.win007.com/Analy/Analysis/2043768.htm"
        # driver.get(url)
        # data = driver.page_source
        bs4_data = BeautifulSoup(data, 'lxml')
        xpath_data = etree.HTML(data)
        # 当前联赛
        pattern_League = re.compile('[\u4e00-\u9fa5a-zA-Z]+')
        mark = True
        try:
            bs4_League_name = bs4_data.select('div[class="league"]')[0].get_text()
        except Exception as e:
            mark = False
        if mark:
            print("可以获取详细的联赛名称和时间")
            # print(bs4_League_name)
            # 获取联赛名称
            self.League_name = pattern_League.findall(bs4_League_name)[0]
            print(self.League_name)
            # 获取详细的联赛名称和时间
            self.detail_League_name = bs4_League_name
            # 获取主队名称 /html/body/div[1]/div[1]/div/div[2]/div/span/text()
            self.Home_team_data["home_team_name"] = bs4_data.select('div[id="homeName"] span')[0].get_text().strip()
            # 当前赛场是第几场
            try:
                self.home_Session = int(xpath_data.xpath('/html/body/div[2]/div[3]/div[3]/table[1]/tbody/tr[2]/td[2]/text()')[0])
            #                                             /html/body/div[2]/div[3]/div[3]/table[1]/tbody/tr[2]/td[2]
            except:
                self.home_Session = None
            # 获取主队积分
            try:
                self.Home_team_data["Home_team_points"] = int(xpath_data.xpath('/html/body/div[2]/div[3]/div[3]/table[1]/tbody/tr[2]/td[9]/font/text()')[0])
            except:
                self.Home_team_data["Home_team_points"] = None
            # 获取主队排名
            try:
                self.Home_team_data["home_rank"] = int(xpath_data.xpath('/html/body/div[2]/div[3]/div[3]/table[1]/tbody/tr[2]/td[10]/text()')[0])
            except:
                self.Home_team_data["home_rank"] = None
            # 获取主队下一场比赛是什么比赛（有没有重要的杯赛)
            try:
                self.Home_team_data["home_Next_game"] = xpath_data.xpath('/html/body/div[2]/div[17]/table[1]/tbody/tr[2]/td[1]/div[2]/text()')[0]
            except Exception as e:
                self.Home_team_data["home_Next_game"] = None
            # 获取主队上一场比赛的日期
            try:
                home_Last_date = xpath_data.xpath('/html/body/div[2]/div[8]/span[1]/table/tbody/tr[2]/td[1]/div[1]/text()')[0]
                # 主队上一场比赛的日期是否比当前日期小三天以上(因为赛事太少暂时调整为2天)
                home_date_rules = self.get_lastgamedate_ruleresult(home_Last_date)
                self.Home_team_data["home_date_rules"] = home_date_rules
            except:
                self.Home_team_data["home_date_rules"] = None

            # --------------- 客队 ---------------------------------------------
            # 客队名称
            self.Away_team_data["Away_team_name"] = bs4_data.select('div[id="guestName"] span')[0].get_text().strip()
            # 客队积分
            try:
                self.Away_team_data["Away_team_points"] = int(
                    xpath_data.xpath('/html/body/div[2]/div[3]/div[3]/table[2]/tbody/tr[2]/td[9]/font/text()')[0])
            except Exception as e:
                self.Away_team_data["Away_team_points"] = None
            # 客队排名
            try:
                self.Away_team_data["Away_rank"] = int(xpath_data.xpath('/html/body/div[2]/div[3]/div[3]/table[2]/tbody/tr[2]/td[10]/text()')[0])
            except Exception as e:
                self.Away_team_data["Away_rank"] = None
            # 客队下一场比赛是什么比赛（有没有重要的杯赛）
            try:
                self.Away_team_data["Away_Next_game"] = xpath_data.xpath('/html/body/div[2]/div[17]/table[2]/tbody/tr[2]/td[1]/div[2]/text()')[0]
            except Exception as e:
                self.Away_team_data["Away_Next_game"] = None

            # 客队上一场比赛的日期上一场比赛的日期
            try:
                Away_Last_date = xpath_data.xpath('/html/body/div[2]/div[8]/span[2]/table/tbody/tr[2]/td[1]/div[1]/text()')[0]
                # 客队上一场比赛的日期上一场比赛的日期是否 比当前日期小三天(因为赛事太少暂时调整为2天)
                Away_date_rules = self.get_lastgamedate_ruleresult(Away_Last_date)
                self.Away_team_data["Away_date_rules"] = Away_date_rules
            except:
                self.Away_team_data["Away_date_rules"] = None
            # 获取主客队 近三场比赛的积分总是是否大于7分
            # Home_team_points = 0
            Home_threegames_points = 0
            # Away_team_points = 0
            Away_threegames_points = 0
            re_integral = re.compile('[0-9]')
            for i in range(2, 5):
                try:
                    Home_score_xpath = \
                    xpath_data.xpath('/html/body/div[2]/div[8]/span[1]/table/tbody/tr[%s]/td[2]/@style' % i)[0]
                    Home_score = re_integral.findall(Home_score_xpath)[0]
                    if int(Home_score) == 2:
                        print("本场比赛积分为0")
                    else:
                        Home_threegames_points = Home_threegames_points + int(Home_score)
                        # print(Home_threegames_points)
                except Exception as e:
                    print("主队近期的第%s场为客场比赛" % (i - 1))
                    try:
                        Awaygame_score_xpath = \
                        xpath_data.xpath('/html/body/div[2]/div[8]/span[1]/table/tbody/tr[%s]/td[4]/@style' % i)[0]
                        Awaygame_score = re_integral.findall(Awaygame_score_xpath)[0]
                        if int(Awaygame_score) == 2:
                            print("本次比赛积分为0")
                        else:
                            Home_threegames_points = Home_threegames_points + int(Awaygame_score)
                            # print(Home_threegames_points)
                    except Exception as e:
                        print("主场和客场积分都获取不到")
            print("主队近三场比赛积分是： ", Home_threegames_points)
            self.Home_team_data["Home_threegames_points"] = Home_threegames_points


            for i in range(2, 5):
                try:
                    Home_score_xpath = \
                    xpath_data.xpath('/html/body/div[2]/div[8]/span[2]/table/tbody/tr[%s]/td[2]/@style' % i)[0]
                    Home_score = re_integral.findall(Home_score_xpath)[0]
                    if int(Home_score) == 2:
                        print("本场比赛积分为0")
                    else:
                        Away_threegames_points = Away_threegames_points + int(Home_score)
                        # print(Away_threegames_points)
                except Exception as e:
                    print("客队近期的第%s场为客场比赛" % (i - 1))
                    try:
                        Awaygame_score_xpath = \
                        xpath_data.xpath('/html/body/div[2]/div[8]/span[2]/table/tbody/tr[%s]/td[4]/@style' % i)[0]
                        Awaygame_score = re_integral.findall(Awaygame_score_xpath)[0]
                        if int(Awaygame_score) == 2:
                            print("本次比赛积分为0")
                        else:
                            Away_threegames_points = Away_threegames_points + int(Awaygame_score)
                            # print(Away_threegames_points)
                    except Exception as e:
                        print("主场和客场积分都获取不到")
            # print(Away_threegames_points)
            self.Away_team_data["Away_threegames_points"] = Away_threegames_points
            print("客场近三场比赛积分：", Away_threegames_points)
            # 主队 和对手的 近一期的比赛结果
            try:
                Home_Victory_or_defeat = \
                xpath_data.xpath('/html/body/div[2]/div[6]/span[1]/table/tbody/tr[2]/td[3]/b/font/@style')[0]
                print("------------Home_Victory_or_defeat", Home_Victory_or_defeat)
                Home_Victory_or_defeat_ = re_integral.findall(Home_Victory_or_defeat)[0]
                print("------------Home_Victory_or_defeat", Home_Victory_or_defeat_)
                if int(Home_Victory_or_defeat_) == 3:
                    self.Home_team_data["Home_Victory_or_defeat_"] = True
                    self.Away_team_data["Away_Victory_or_defeat_"] = False
                elif int(Home_Victory_or_defeat_) == 2:
                    self.Home_team_data["Home_Victory_or_defeat_"] = False
                    self.Away_team_data["Away_Victory_or_defeat_"] = True
                elif int(Home_Victory_or_defeat_) == 1:
                    try:
                        # 如果比平了 就获取客队的名称
                        self.past_games_Away_name["past_games_Away_name"] = \
                        xpath_data.xpath('/html/body/div[2]/div[6]/span/table/tbody/tr[2]/td[4]/text()')[0]
                    except Exception as e:
                        print("获取不到往期比赛中客队名称")
                else:
                    self.Home_team_data["Home_Victory_or_defeat_"] = False
                    self.Away_team_data["Away_Victory_or_defeat_"] = False
            except Exception as e:
                print("获取不到近一期的对赛结果 Home_Victory_or_defeat_ 为 False")
                self.Home_team_data["Home_Victory_or_defeat_"] = False
                self.Away_team_data["Away_Victory_or_defeat_"] = False

            # 主队和客队最近一次比赛的时间
            try:
                Last_date_ = xpath_data.xpath('/html/body/div[2]/div[6]/span[1]/table/tbody/tr[2]/td[1]/div[1]/text()')[
                    0]
                #                              /html/body/div[2]/div[8]/span[2]/table/tbody/tr[2]/td[1]/div[1]/text()
                print("Last_date_", Last_date_)
                self.battleTime = self.Battle_time(Last_date_)
            except Exception as e:
                print("获取不到上次对战时间")
            print("联赛名称、当前比赛是第几场，主场数据、客场数据", self.League_name,self.home_Session,self.Home_team_data,self.Away_team_data,self.battleTime,self.past_games_Away_name)
            return self.League_name,self.detail_League_name,self.home_Session,self.Home_team_data,self.Away_team_data,self.battleTime,self.past_games_Away_name
        else:
            print("本场数据丢失")
            return "N"
        # print(self.League_name)
    def get_lastgamedate_ruleresult(self,lastdate):
        # 当前日期
        Date_rules = "N"
        now_date = datetime.datetime.now().date()
        # 当前日期减去三天(因为赛事太少暂时调整为2天)
        now_data_last3 = now_date + datetime.timedelta(days=-2)
        # 上一场比赛日期
        d = lastdate
        # print(type(d))
        lastgame_data = datetime.datetime.strptime(d, "%y-%m-%d").date()
        if lastgame_data < now_data_last3:
            Date_rules = "Y"
        # print(Date_rules)
        return Date_rules

    def Battle_time(self,lastdate):
        # 给定的日期字符串
        date_str = lastdate
        # 将字符串转换为日期对象
        given_date = date_.strptime(date_str, "%y-%m-%d")
        # 获取当前日期
        current_date = date_.now()
        # 计算5个月前的日期
        five_months_ago = current_date - relativedelta(months=6)

        # 比较日期
        if given_date < five_months_ago:
            print("给定的日期早于当前时间5个月")
            return True
        else:
            print("给定的日期不早于当前时间5个月")
            return False






if __name__ == '__main__':
    DetailsD = Details_Rule(Conf.get_driver(),"https://m.win007.com/Analy/Analysis/1974974.htm")
    DetailsD.Three_points()
    DetailsD.get_lastgamedate_ruleresult("21-08-28")



