import time

from config import Conf

from selenium import webdriver

from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

class Driver_data:
    # 初始化文件
    def __init__(self,driver_path):
        self.driver_path = driver_path
        try:
            self.driver = webdriver.Chrome(executable_path=self.driver_path)
        except Exception as e:
            time.sleep(1)
            self.driver = webdriver.Chrome(executable_path=self.driver_path)
    def List_driver(self,Schedule_url):
        self.driver.get(Schedule_url)
        schedule_data = self.driver.page_source
        return schedule_data
    def Details_driver(self,analyze_url):
        self.driver.set_page_load_timeout(60)
        retry_count = 0
        max_retries = 3
        checkbox = None
        while retry_count < max_retries:
            try:
                self.driver.get(analyze_url)
                break  # 如果页面成功加载，跳出循环
            except TimeoutException:
                retry_count += 1
                print(f"Timeout occurred. Retry {retry_count} of {max_retries}.")
                self.driver.refresh()  # 刷新页面
        if retry_count == max_retries:
            print(f"重试三次加载页面失败Failed to load the page after {max_retries} attempts.")
            # 在这里，你可以选择关闭driver，抛出异常，或采取其他操作
        # try:
        #     checkbox = WebDriverWait(self.driver, 30).until(
        #         EC.presence_of_element_located((By.XPATH, ))
        #     )
        # except Exception as e:
        #     print("")
        # time.sleep(2)
        try:
            checkbox = WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
            )
        except Exception as e:
            print("没有获取到广告图片关闭按钮")
        if checkbox:
            try:
                checkbox.click()
                print("关闭了广告")
            except Exception as e:
                time.sleep(5)
                checkbox_ = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                )
                try:
                    checkbox_.click()
                    print("再次尝试点击关闭广告")
                except Exception as e:
                    time.sleep(5)
                    checkbox__ = WebDriverWait(self.driver, 20).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                    )
                    try:
                        checkbox__.click()
                        print("第三次尝试点击关闭广告")
                    except Exception as e:
                        print("第三次尝试关闭广告失败")


        print("等待页面加载数据")
        # time.sleep(1)
        # self.driver.find_element(By.CSS_SELECTOR, "body > div > img").click()
        # try:
        #     data = WebDriverWait(self.driver, 20).until(
        #         EC.presence_of_element_located((By.XPATH, '/html/body/div[2]/div[3]/div[3]/table[1]/tbody/tr[2]/td[2]'))
        #     )
        # except Exception as e:
        #     print("加载页面数据超时")
        Rounds_ = False
        try:
            Rounds = WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.XPATH, '/html/body/div[2]/div[3]/div[3]/table[1]/tbody/tr[2]/td[2]'))
            )
            if Rounds.text:
                Rounds_ = True
        except Exception as e:
            print("获取分析页面数据抛异常了")
        if Rounds_:
            print("获取到了页面数据")
        else:
            print("获取页面数据失败")
        analyze_data =self.driver.page_source
        self.driver.close()
        self.driver.quit()
        return analyze_data

if __name__ == '__main__':
    Driver_data()



