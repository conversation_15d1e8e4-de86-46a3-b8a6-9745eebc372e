import os
import yaml

class YamlReader:
# 初始化，文件是否存在，参数yamlf 表示yaml 文件
    def __init__(self,yamlf):
        if os.path.exists(yamlf):
            self.yamlf = yamlf
        else:
            #如果文件不存在 则抛出异常
            raise FileExistsError("文件不存在")
        self._data = None
        self._data_all = None

# yaml 读取
    def data(self):
        #假如第一次调用_data,就读取yaml 文档，如果不是就直接返回之前报错的数据
        #如果不存在 self._data 那么我们就读取 否则我们就返回
        if not self._data:
            with open(self.yamlf,"rb") as f:
                self._data = yaml.safe_load(f)
        return self._data
# 多文档读取，比如多个数据库配置信息读取
    def data_all(self):
        if not self._data_all:
            with open(self.yamlf,"rb") as f:
                self._data_all = list(yaml.safe_load_all(f))
        return self._data_all

