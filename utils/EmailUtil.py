from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib
from datetime import datetime
#初始化
    # smtp地址、用户名、密码、邮件接收者、邮件标题、邮件内容、邮件附件
class SendEmail:
    def __init__(self,smtp_addr,username,password,rec,recv,title,content=None,file=None):
        #host 地址
        self.smtp_addr = smtp_addr
        self.username = username
        self.password = password
        self.rec = rec
        self.recv = recv
        self.title = title
        self.content = content
        self.file = file
# 发送邮件方法
    def send_mail(self):
        # MIME
        msg = MIMEMultipart()
        # 初始化邮件信息,首先如果要添加正文的化 就是msg.attach(self.content);# 如果要发送字符串 信息的化 我们需要使用另外一个类 MIMEText
        msg.attach(MIMEText(self.content,_charset="utf-8"))
        # 标题
        msg["Subject"] = self.title
        # 发送者账号
        msg["From"] = self.username
        # 发给谁的
        print(self.recv)
        # addlist = ",".join(self.recv)
        # print("addlist", addlist)
        # 获取当前时间
        now = datetime.now()
        # 设置目标时间为15:00
        target_time = now.replace(hour=15, minute=0, second=0, microsecond=0)
        # 判断当前时间是否大于15:00
        # if now > target_time:
        if not self.rec:
            self.recv =self.recv
            msg["To"] = ",".join(self.recv)
            # print(msg["To"])
        else:
            addlist = [item for item in self.recv if "ajog0018" in item]
            self.recv = addlist
            msg["To"] = ",".join(addlist)
            # print(msg["To"])
        # msg["To"] = addlist
        # 邮箱附件
            #判断是否有附件
        if self.file:
            # MIMEText 读取一下邮件
            att = MIMEText(open(self.file).read())
            #设置内容类型
            att["Content-Type"] = 'application/octet-stream'
            #设置附件的头
            att["Conten-Disposition"] = 'attachment;filename="%s"'%self.file
            #将内容附加到邮件主题中
            msg.attach(att)
        #登录邮件服务器
        # self.smtp = smtplib.SMTP(self.smtp_addr,port=25)
        # self.smtp.ehlo() # 向邮箱发送SMTP 'ehlo' 命令
        # self.smtp.starttls()
        self.smtp = smtplib.SMTP()
        self.smtp = smtplib.SMTP_SSL(self.smtp_addr)
        # lls 发送的端口号是 465
        self.smtp.connect(self.smtp_addr,465)
        # self.smtp.ehlo() # 向邮箱发送SMTP 'ehlo' 命令
        # self.smtp.starttls()
        # self.smtp.set_debuglevel(1)
        self.smtp.login(self.username,self.password)
    #发送邮件
        self.smtp.sendmail(self.username,self.recv,msg.as_string())
        #关闭服务器
        self.smtp.quit()

if __name__ == '__main__':
    from config.Conf import ConfigYmal
    email_info = ConfigYmal().get_email_info()
    # 初始化类
    # email服务器地址
    smtp_addr = email_info["smtpserver"]
    username = email_info["username"]
    password = email_info["password"]
    # 接收者
    recv = email_info["receiver"]
    # title 随便写一个
    email = SendEmail(smtp_addr,username,password,recv,"测试")
    email.send_mail()
    # 封装公共方法 放在common 的base里面
    # 应用数据发送





