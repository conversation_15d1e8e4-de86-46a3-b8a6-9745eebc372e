# coding=gbk
import sys
import os
# sys.path.append(r"C:\Jenkins_workspace\workspace\RecommendedSystem_JOB")
# sys.path.append(r"C:\Jenkins_workspaces\workspace\RecommendedSystem")
script_dir = os.path.dirname(__file__)  # 获取脚本所在目录
project_dir = os.path.dirname(script_dir)  # 假设项目根目录是脚本所在目录的上级
sys.path.append(project_dir)
import time
from bs4 import BeautifulSoup
import re
from common import ExcelConfig
from utils.ExcelUtil import ExcelReader
from config import Conf
from config.Conf import ConfigYmal
import datetime
from common.ExcelData import Data
from utils.DriverUtil import Driver_data
from utils.RuleUtils import Details_Rule
from common import Base
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime, timedelta
import time
import random
import threading
import queue
import ast
from selenium import webdriver
from lxml import etree
from bs4 import BeautifulSoup
import json
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
# from pychrome import Chrome
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import NoSuchElementException
from twocaptcha import TwoCaptcha


pattern = re.compile(r'\n\s*([\w\d]+)\s*(\d{2}-\d{2} \d{2}:\d{2}).*?\s*:\s*(.*?)\s*(https://.*)')
class Game_Recommendation:
    def __init__(self):
        # self.driver = webdriver.Chrome(executable_path="D:\software\chromedriver\chromedriver_win32\chromedriver.exe")
        # driver 程序路径
        self.driver_path = Conf.get_driver()
        self.url_id_list = list()
        self.case_file = os.path.join(Conf.get_data_excel_path(), ConfigYmal().get_excel_file())
        # 测试用例sheet名称
        self.sheet_name = ConfigYmal().get_excel_sheet()
        # 获取联赛列表 _League_data,所有洲际杯赛,所有国家队比赛
        self._League_data,self._Club_Intercontinental_Cup,self._Country_games = Data(self.case_file,self.sheet_name).get_Filter_events()
        #   获取gamelist_file 文件名称和路径
        self.gamelist_file = os.path.join(Conf.get_data_excel_path(),ConfigYmal().get_gamelist_file())
        # 读取国际化映射文件
        self.Internationalization_mapping = os.path.join(Conf.get_data_excel_path(),
                                                         ConfigYmal().Internationalization_mapping())
        # 国家名称为中文的映射文件
        self.Internationalization_mapping_ = os.path.join(Conf.get_data_excel_path(),
                                                          ConfigYmal().Internationalization_mapping_())
        self.scheduler = BackgroundScheduler()
        self.scheduled_urls = set()
        self.past_game_offset = timedelta()
        self.shutdown_lock = threading.Lock()
        self.stop_threads = False
        # 这是一个全局的线程列表
        self.threads = []
        # 定义一个队列 用来存放需要马上下单的元组数据
        self.results_q = queue.Queue()
        # 这个是一个用来比赛结果输赢状态的队列
        self.bet_result_q = queue.Queue()
        self.start_signal_sent = False
        self.stop_event = threading.Event()
        self.signal_file_path = os.path.join(Conf.get_data_excel_path(),ConfigYmal().get_signal_file())
    def Game_acquisition(self):
        # case_file = os.path.join(Conf.get_data_excel_path(), ConfigYmal().get_excel_file())
        # # 测试用例sheet名称
        # sheet_name = ConfigYmal().get_excel_sheet()
        # # 获取联赛列表 _League_data
        # _data,_League_data,_Club_Intercontinental_Cup,_Country_games = ExcelReader(case_file, sheet_name).data()
        # https://m.win007.com/analy/Analysis/2008075.htm
        # url 前缀
        start_url = ConfigYmal().get_start_url()
        url_Prefix = ConfigYmal().get_url_Prefix()
        # url_Prefix = "https://m.win007.com/analy/Analysis/"
        # url后缀
        url_Suffix = ".htm"
        result_data = Driver_data(self.driver_path).List_driver(start_url)
        # ----------------------------------------------------------------------------------
        soup = BeautifulSoup(result_data,'lxml')
        # 赛事id
        pattern_id = re.compile('\d+')
        #联赛名称
        pattern_League = re.compile('[\u4e00-\u9fa5a-zA-Z]+')
        #开赛时间
        pattern_League_time = re.compile('[^\u4e00-\u9fa5^a-z^A-Z]+')
        # result = soup.find_all(attrs={"onclick":"toFenXi(\d*)"})
        Parsed_data = {}
        result = soup.select('ul[id="todaySchedule"] li')
        result2 = soup.select('ul[id="nextSchedule"] li')
        Schedule_date = soup.select('div[id="scheduleList"] h3')[0].get_text()
        rule_re =re.compile('[^\s^\u4e00-\u9fa5]+')
        result_date_str = rule_re.findall(Schedule_date)[0]
        today_date = datetime.now().date()
        today_str = today_date.strftime("%Y-%m-%d")
        print("赛程中凌晨后的日期", result_date_str)
        print("今日赛程日期", today_str)
        # 当天是否有国际比赛日，如果有
        International_game1 = False
        International_game2 = False
        for data2 in result2:
            League_data2 = data2.select('.game')[0].get_text()
            League_tomorrow_name = pattern_League.findall(League_data2)[0]
            if League_tomorrow_name in self._Country_games:
                # print(League_tomorrow_name)
                International_game2 = True
                pass
        if International_game2:
            print(International_game2 , "今天有国际比赛International_game2")

        for data in result:
            League_data = data.select('.game')[0].get_text()
            # 今天的联赛名称
            League_today_name = pattern_League.findall(League_data)[0]
            if League_today_name in self._Country_games:
                # print(League_today_name)
                International_game1 = True
                pass
        if International_game1:
            print(International_game1 , "今天有国际比赛International_game1")

        if not International_game1 and not International_game2:
            if result_date_str == today_str:
                for data2 in result2:
                    League_data2 = data2.select('.game')[0].get_text()
                    League_tomorrow_name = pattern_League.findall(League_data2)[0]
                    # print(League_tomorrow_name)
                    League_tomorrow_time = pattern_League_time.findall(League_data2)[0]
                    # print(League_tomorrow_time)
                    League_id2 = data2.get("onclick")
                    League_tomorrow_id = pattern_id.findall(League_id2)[0]
                    # print(League_tomorrow_id)
                    # if League_tomorrow_name not in self._Country_games:
                    if League_tomorrow_name in self._League_data:
                        if League_tomorrow_time > time.strftime("%H:%M", time.localtime()):
                            print("明天的比赛时间大于当前时间")
                            self.url_id_list.append(url_Prefix + League_tomorrow_id + url_Suffix)
                    # else:
                    #     print("今天有国家队比赛")
            else:
                for data in result:
                    League_data = data.select('.game')[0].get_text()
                    # 今天的联赛名称
                    League_today_name = pattern_League.findall(League_data)[0]
                    # print(League_today_name)
                    # 今天的开赛时间
                    League_today_time = pattern_League_time.findall(League_data)[0]
                    # print(League_today_time)
                    #通过正则表达式把 哈萨乙20:00 分成赛程和 时间两部分
                    # 获取id
                    League_id = data.get("onclick")
                    # 今天的比赛id
                    League_today_id = pattern_id.findall(League_id)[0]
                    # print(League_today_id)
                    # if League_today_name not in self._Country_games:
                    if League_today_name in self._League_data:
                        if League_today_time > time.strftime("%H:%M", time.localtime()):
                            self.url_id_list.append(url_Prefix+League_today_id+url_Suffix)
                for data in result2:
                    League_data2 = data.select('.game')[0].get_text()
                    League_tomorrow_name = pattern_League.findall(League_data2)[0]
                    # print(League_tomorrow_name)
                    League_tomorrow_time = pattern_League_time.findall(League_data2)[0]
                    # print(League_tomorrow_time)
                    League_id2 = data.get("onclick")
                    League_tomorrow_id = pattern_id.findall(League_id2)[0]
                    # print(League_tomorrow_id)
                    # if League_tomorrow_name not in self._Country_games:
                    if League_tomorrow_name in self._League_data:
                        self.url_id_list.append(url_Prefix+League_tomorrow_id+url_Suffix)
            # print(self.url_id_list)
            return self.url_id_list
        else:
            return "今天有国际比赛"
    def process_item_regex(self,item):
        match = pattern.match(item)
        if match:
            # If the pattern matches, return the groups
            return match.groups()
        else:
            # If the pattern does not match, return None
            return None
    def game_Details(self):
        # 如果当天没有国际比赛或者国家队的洲际杯赛 开赛分析比赛
        # 开赛第七场开赛分析比赛
        # 结束场之前分析比赛
        # 找到两支球队中积分比较高的球队
        # 下一场比赛如果没有没有 洲际俱乐部杯赛 继续分析
        # 积分高的球队 在升级区前后五位   这一步先忽略 如果以后有需要就加上它
        # 积分低的球队 不在降级区
        # 积分高的球队上一次比赛 距离本场比赛大于3天休息日
        # 积分高的球队 过去三场比赛积分大于6分
        _data = ExcelReader(self.case_file,self.sheet_name).data()
        url_list = self.Game_acquisition()
        print(url_list)
        # 推荐比赛列表
        Recommend_game_list = list()
        mark1 = False
        if type(url_list) == list:
            for details_url in url_list:
                #详情页中: 联赛名称、比赛轮次、主队数据、客队数据1
                result_ = Details_Rule(self.driver_path,details_url).Three_points()
                if result_ != "N":
                    try:
                        detail_League_name,detail_League_time,detail_home_Session,detail_Home_team_data,detail_Away_team_data,battleTime,past_games_Away_name = result_
                        mark1 = True
                    except Exception as e:
                        print("Three_points() 返回的数据不完整")
                        mark1 = False
                    if mark1:
                        # 拿到联赛的自定义数据
                        League_line = Data(self.case_file,self.sheet_name).get_case_pre(detail_League_name)
                        if League_line == None:
                            League_line = Data(self.case_file,self.sheet_name).get_case_pre(detail_League_name[:-1])
                        # 如果当天没有国际比赛或者国家队的洲际杯赛 开始分析比赛,在列表页中 Game_acquisition(self) 就已经过滤掉了
                        # if detail_League_name not in self._Country_games:
                        # 如果大于六场并且小于结束场次 就开始分析比赛
                        # # print("启始场次:", int(League_line[ExcelConfig.DataConfig.Number_openings]))
                        # print("当前场次:", detail_home_Session)
                        # print("结束场次:", int(League_line[ExcelConfig.DataConfig.Ending_games]))
                        # 当前赛程大于开始场 小于结束场
                        if detail_home_Session != None:
                            if int(League_line[ExcelConfig.DataConfig.Number_openings]) < detail_home_Session < int(League_line[ExcelConfig.DataConfig.Ending_games]):
                                # 客场积分不为None
                                if detail_Away_team_data["Away_team_points"] != None and detail_Home_team_data["Home_team_points"] != None:
                                    # 主队积分高于客队
                                    if (detail_Home_team_data["Home_team_points"]-detail_Away_team_data["Away_team_points"]) > 6:
                                        # 主队下一场比赛没有 洲际俱乐部杯赛
                                        if detail_Home_team_data["home_Next_game"] not in self._Club_Intercontinental_Cup and detail_Home_team_data["home_Next_game"] != None:
                                            # 主队上次比赛 到今天超过3天休息日(因为赛事太少暂时调整为2天)
                                            if detail_Home_team_data["home_date_rules"] == "Y":
                                                # 客队不在降级区
                                                if detail_Away_team_data["Away_rank"] != None and detail_Away_team_data["Away_rank"] < (int(League_line[ExcelConfig.DataConfig.Downgrade])-2):
                                                    # 主队过去三场比赛 积分超过 5分
                                                    # if detail_Home_team_data["Home_threegames_points"] >= 5:
                                                    #     Recommend_game = detail_League_time + ": "+ detail_Home_team_data["home_team_name"] + " VS " + detail_Away_team_data["Away_team_name"]
                                                    #     Recommend_game_list.append(Recommend_game+" 主队积分高于客队" + " "+details_url)
                                                    if detail_Home_team_data["Home_threegames_points"] >= 5:
                                                        # 当对战距今下于约5个月
                                                        if not battleTime:
                                                            # 如果是比了 判断强队是否在客队平的
                                                            if past_games_Away_name:
                                                                if detail_Home_team_data[
                                                                    "home_team_name"] == past_games_Away_name:
                                                                    Recommend_game = detail_League_time + ": " + \
                                                                                     detail_Home_team_data[
                                                                                         "home_team_name"] + " VS " + \
                                                                                     detail_Away_team_data[
                                                                                         "Away_team_name"]
                                                                    Recommend_game_list.append(
                                                                        Recommend_game + " 主队积分高于客队" + " " + details_url)

                                                        else:
                                                            Recommend_game = detail_League_time + ": " + \
                                                                             detail_Home_team_data[
                                                                                 "home_team_name"] + " VS " + \
                                                                             detail_Away_team_data["Away_team_name"]
                                                            Recommend_game_list.append(
                                                                Recommend_game + " 主队积分高于客队" + " " + details_url)
                                    # 客队积分高于主队
                                    elif (detail_Away_team_data["Away_team_points"]-detail_Home_team_data["Home_team_points"]) > 6:
                                        # 客队下一场比赛没有 洲际俱乐部杯赛
                                        if detail_Away_team_data["Away_Next_game"] not in self._Club_Intercontinental_Cup and detail_Away_team_data["Away_Next_game"] != None:
                                            # 客队上次比赛到今天超过3天休息日(因为赛事太少暂时调整为2天)
                                            if detail_Away_team_data["Away_date_rules"] == "Y":
                                                # 主队不在降级区
                                                if detail_Home_team_data["home_rank"] <(int(League_line[ExcelConfig.DataConfig.Downgrade])-2):
                                                    # 客队过去三场比赛积分超过5分
                                                    if detail_Away_team_data["Away_threegames_points"] >=5:
                                                        # Recommend_game = detail_League_time + ": "+ detail_Home_team_data["home_team_name"] + " VS " + detail_Away_team_data["Away_team_name"]
                                                        # Recommend_game_list.append(Recommend_game+ " "+details_url)
                                                        if not battleTime:
                                                            if past_games_Away_name:
                                                                if detail_Away_team_data[
                                                                    "Away_team_name"] == past_games_Away_name:
                                                                    Recommend_game = detail_League_time + ": " + \
                                                                                     detail_Home_team_data[
                                                                                         "home_team_name"] + " VS " + \
                                                                                     detail_Away_team_data[
                                                                                         "Away_team_name"]
                                                                    Recommend_game_list.append(
                                                                        Recommend_game + " " + details_url)
                                                        else:
                                                            Recommend_game = detail_League_time + ": " + \
                                                                             detail_Home_team_data[
                                                                                 "home_team_name"] + " VS " + \
                                                                             detail_Away_team_data["Away_team_name"]
                                                            Recommend_game_list.append(
                                                                Recommend_game + " " + details_url)
                    else:
                        print("mark1:"+mark1)
                else:
                    time.sleep(1)
            print("Recommend_game_list", Recommend_game_list)
            # 将比赛列表转换为 元组list
            if Recommend_game_list:
                output_tuples = [self.process_item_regex(item) for item in Recommend_game_list]
                print("output_tuples: ", output_tuples)
                with open(self.gamelist_file, 'w', encoding='utf8') as f:
                    f.write(str(output_tuples))
                Recommend_game_str = "\n".join(Recommend_game_list)
                # print("今天推荐的比赛是", Recommend_game_str)
                return Recommend_game_str
            else:
                return "今天没有合适的比赛"
        else:
            print("今天有国际比赛")
            return "今天有国际比赛"
    def read_file(self):
        with open(self.gamelist_file, 'r', encoding='utf8') as f:
            output_tuples = ast.literal_eval(f.read())
            print("output_tuples_: ", output_tuples)
        return output_tuples
    def bet_stop_q(self):
        # bet_result_q
        false_count = 0
        while not self.stop_event.is_set():
            try:
                value = self.bet_result_q.get(timeout=1)  # 设置一个超时时间，避免无限阻塞
                if value is None:
                    break
                if value is False:
                    false_count += 1
                    if false_count >= 3:
                        print("连续三次获取到False值！标记操作进行。")
                        self.stop_event.set()
                        self.results_q.put(None)
                        self.bet_result_q.put(None)
                        break
                else:
                    false_count = 0
                print("获取到值:", value)
                # self.results_q.task_done()
                time.sleep(20)
            except queue.Empty:
                pass

    def start_driver(self):
        drivers = {}
        print("开始在网站下单的逻辑")

        chrome_options = webdriver.ChromeOptions()
        # # 打开 Chrome 浏览器并加载配置
        chrome_options.add_argument(
            "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
        # C:\Users\<USER>\AppData\Local\Chromium\User Data
        # chrome_options.add_argument(f"user-agent={user_agent}")
        chrome_options.add_argument(
            r'--load-extension=C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default\Extensions\dknlfmjaanfblgfdfebhijalfmhmjjjo\0.4.5_0')

        chrome_options.binary_location = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        driver = webdriver.Chrome(options=chrome_options,
                                  executable_path=r'C:\project\RecommendationSystem\data\chromedriver.exe')
        time.sleep(10)
        driver.get("https://sportsbet.io/zh/sports")
        # 2 | setWindowSize | 1920x1040 |  |
        # driver.set_window_size(1920, 1040)
        # driver.set_window_size(1936, 1056)
        time.sleep(3)
        driver.maximize_window()
        time.sleep(3)
        football_e = False
        try:
            football_ = WebDriverWait(driver, 120).until(
                EC.presence_of_element_located((By.XPATH,
                                                "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
            )
            football_e = True
        except Exception as e:
            print("获取足球元素报错")

        if football_e:
            football_.click()
        time.sleep(2)
        actions = False
        try:
            # element = driver.find_element(By.XPATH,
            #                                    "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]")
            element = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH,
                                                "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
            )
            actions = True
        except Exception as e:
            print("移动鼠标报错了")
        if actions:
            actions = ActionChains(driver)
            actions.move_to_element(element).perform()
            # 5 | mouseOut | css=.active > .category-bar__CategoryIcon-sc-4cedq1-3 |  |
            element = driver.find_element(By.CSS_SELECTOR, "body")
            actions = ActionChains(driver)
            actions.move_to_element(element).perform()

        drivers["driver"] = driver
        return drivers
    def read_results_q(self):
        # 是否可以下单
        bet_order = True
        # team_en_ = False
        # 最终可以下单的球队名称
        # order_Team_name = ['']
        # 韩国K联 这个名称 应该是韩K联 ?????????
        # self.results_q.put(['韩K联', '10-29 13:00', '蔚山现代 VS 大邱FC 主队积分高于客队','https://m.titan007.com/Analy/ShiJian/2488517.htm', 0])
        # self.results_q.put(['葡甲', '10-29 22:00', '费伦斯 VS AVS', 'https://m.titan007.com/analy/Analysis/2421624.htm', 0])
        # self.results_q.put(['德甲', '10-29 22:30', '法兰克福 VS 多特蒙德', 'https://m.titan007.com/analy/Analysis/2412910.htm', 1])
        # self.results_q.put(
        #     ('韩K联', '08-06 16:00', '大邱FC VS 大田韩亚市民 主队积分高于客队', 'https://m.titan007.com/Analy/Analysis/2354016.htm'))
        # 这里是要执行下单的


        # driver = self.start_driver()["driver"]


        # # NOPECHA_KEY = 'kbs0hjj2xc_L63JZLHL'
        # # driver.get(f"https://nopecha.com/setup#{NOPECHA_KEY}")  # 使用您的API key配置NopeCHA
        # #
        # # # 添加一些等待时间，以确保扩展加载和设置完成
        # # driver.implicitly_wait(10)  # 这里的时间可以根据需要调整
        # #
        # # # 测试NopeCHA是否工作：导航到一个包含CAPTCHA的测试页面
        # # driver.get('https://nopecha.com/demo/hcaptcha')  # 替换为您想要测试的页面URL
        # time.sleep(30)
        # driver.get("https://sportsbet.io/zh/sports")
        # # 2 | setWindowSize | 1920x1040 |  |
        # # driver.set_window_size(1920, 1040)
        # # driver.set_window_size(1936, 1056)
        # driver.maximize_window()
        # # 3 | click | css=.sc-hatQeL:nth-child(2) .category-bar__CategoryIcon-sc-4cedq1-3 |  |
        # time.sleep(5)
        # # driver.find_element(By.XPATH,
        # #                          "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]").click()
        # football_e = False
        # try:
        #     football_ = WebDriverWait(driver, 120).until(
        #         EC.presence_of_element_located((By.XPATH,
        #                                         "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
        #     )
        #     football_e = True
        # except Exception as e:
        #     print("获取足球元素报错")
        #
        # if football_e:
        #     football_.click()
        #
        # actions = False
        # try:
        #     # element = driver.find_element(By.XPATH,
        #     #                                    "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]")
        #     element = WebDriverWait(driver, 60).until(
        #         EC.presence_of_element_located((By.XPATH,
        #                                         "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
        #     )
        #     actions = True
        # except Exception as e:
        #     print("移动鼠标报错了")
        # if actions:
        #     actions = ActionChains(driver)
        #     actions.move_to_element(element).perform()
        #     # 5 | mouseOut | css=.active > .category-bar__CategoryIcon-sc-4cedq1-3 |  |
        #     element = driver.find_element(By.CSS_SELECTOR, "body")
        #     actions = ActionChains(driver)
        #     actions.move_to_element(element).perform()

        # 保活测试任务开始时间
        # keepAlive_start_time = datetime.now() + timedelta(minutes=30)
        # print("保活任务开始时间", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # self.results_q.put(['韩K联', '10-29 13:00', '蔚山现代 VS 大邱FC',
        #                     'https://m.titan007.com/Analy/ShiJian/2488517.htm',1])

        while not self.stop_event.is_set():
            Home_ = False
            Away_ = False
            value_ = False
            # chro_ = True
            send_e = True

            try:
                value = self.results_q.get(timeout=1)  # 设置一个超时时间，避免无限阻塞
                print("results_q 队列获取到元素", value)

                if isinstance(value[-1], int):  # 检查最后一个元素是否为整型
                    del value[-2]  # 删除倒数第二个元素
                else:
                    del value[-1]  # 删除最后一个元素

                # 检查第二个元素中是否包含"主队"，如果包含，只保留"主队"之前的字符
                if '主队' in value[2]:
                    value[2] = value[2][:value[2].index('主队') + len('主队')]

                list_of_strings = [str(item) for item in value]
                # 现在我们可以连接列表中的字符串了
                string_of_items = ' '.join(list_of_strings)
                Base.send_email(title="需要处理", content=string_of_items)
                if value == None:
                    break
                else:
                    value_ = True
            except queue.Empty:
                pass
                # try:
                    # driver.execute_script("return true;")
                # except Exception as e:
                    # #在保活期间 如果关闭了浏览器 会在这里抛出异常
                    # # Error occurred: Message: no such window: target window already closed
                    # # from unknown error: web view not found
                    # #   (Session info: chrome=119.0.6045.200)
                    # print("Error occurred:", e)
                    # while True:
                        # with open(self.signal_file_path, 'r') as file:
                            # data = json.load(file)
                            # if data.get('status') == '1':
                                # driver = self.start_driver()["driver"]
                                # self.write_status(self.signal_file_path, '0')
                                # time.sleep(2)
                                # break
                        # time.sleep(10)  # 每10秒检查一次状态文件

                    # chro_ = False
                # # 保活测试任务
                # # keepAlive_start_time = datetime.now()
                # if datetime.now() >= keepAlive_start_time:
                    # print("保活测试记录时间:", datetime.now())
                    # # 开始进行 点击联赛的操作
                    # random_slpe_ = random.uniform(5, 10)
                    # time.sleep(random_slpe_)
                    # bet_league = False
                    # # time.sleep(8)
                    # try:
                        # # liansai_box = WebDriverWait(driver, 30).until(
                        # #     EC.presence_of_element_located((By.LINK_TEXT, "联赛"))
                        # # )

                        # liansai_box = WebDriverWait(driver, 60).until(
                            # EC.presence_of_element_located((By.XPATH,
                                                            # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a"))
                        # )
                        # # liansai_box = driver.find_element(By.XPATH,
                        # #                     "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a").click()
                        # bet_league = True
                    # except Exception as e:
                        # print("获取联赛选项元素抛异常了")
                        # driver.refresh()
                        # time.sleep(16)
                        # try:
                            # liansai_box = WebDriverWait(driver, 60).until(
                                # EC.presence_of_element_located((By.XPATH,
                                                                # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a"))
                            # )
                            # # liansai_box = driver.find_element(By.XPATH,
                            # #                     "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a").click()
                            # bet_league = True
                        # except Exception as e:
                            # print("刷新后再次尝试 获取联赛选项元素还是抛异常了")

                    # if bet_league:
                        # liansai_box.click()
                        # time.sleep(random_slpe_)
                    # # 开始进行点击足球操作
                    # football_e = False
                    # try:
                        # football_ = WebDriverWait(driver, 60).until(
                            # EC.presence_of_element_located((By.XPATH,
                                                            # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
                        # )
                        # football_e = True
                    # except Exception as e:
                        # print("获取足球元素报错")

                    # if football_e:
                        # football_.click()

                    # actions = False
                    # try:
                        # element = WebDriverWait(driver, 60).until(
                            # EC.presence_of_element_located((By.XPATH,
                                                            # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
                        # )
                        # actions = True
                    # except Exception as e:
                        # print("移动鼠标报错了")
                    # if actions:
                        # actions = ActionChains(driver)
                        # actions.move_to_element(element).perform()
                        # element = driver.find_element(By.CSS_SELECTOR, "body")
                        # actions = ActionChains(driver)
                        # actions.move_to_element(element).perform()


                    # keepAlive_start_time = datetime.now() + timedelta(minutes=30)



            # # if value_ and chro_:
            # if value_:
                # order_Team_name = ['']
                # team_en_ = False
                # if value is None:
                    # break
                # if value and value is not None:
                    # # ('韩国K联', '08-06 16:00', '首尔芦原联 VS 大邱FCB队  主队积分高于客队','https://m.titan007.com/Analy/Analysis/2354016.htm')
                    # # 联赛名称
                    # league_matches = value[0]
                    # Home_and_away = value[2]
                    # print("联赛和主客队的名称", league_matches, Home_and_away)
                    # if '主队积分高于客队' in Home_and_away:
                        # Home_team = Home_and_away.split(' VS ')[0]
                        # Home_ = True
                        # # Visitors  = Home_and_away.split(' VS ')[1].split(' 主队积分高于客队')[0]
                    # else:
                        # # Home_team = Home_and_away.split(' VS ')[0]
                        # Visitors  = Home_and_away.split(' VS ')[1]
                        # print("Visitors: ",Visitors)
                        # Away_ = True
                    # # print("主客队的名称是", Home_team, Visitors)
                # # 球队映射标识
                # Team_mapping = False
                # en_home_ = False
                # en_Visitors_ = False

                # with open(self.Internationalization_mapping_, 'r', encoding='utf8') as file:
                    # for line in file:
                        # data = json.loads(line)
                        # # print(type(data))
                        # if league_matches in data:
                            # # 返回联赛对应的国家名称（取字典中的第一个key）
                            # country = list(data[league_matches].keys())[0]
                            # print("联赛对应的国家名称", country)
                # # with open(self.Internationalization_mapping_, 'r', encoding='utf8') as file:
                # #     for line in file:
                # #         data = json.loads(line)
                            # for league_data in data.values():
                                # # print(league_data)
                                # for country_data in league_data.values():
                                    # # print(country_data)
                                    # # if "team" in country_data and Home_team in country_data["team"]:
                                    # if Home_:
                                        # try:
                                            # en_Home_team = country_data["team"][Home_team]
                                            # print("主场英文名称", en_Home_team)
                                            # en_home_ = True
                                            # # 暂时去掉 切换英文 的开关
                                            # Team_mapping = True
                                        # except Exception as e:
                                            # pass
                                            # # print(f"{Home_team} 获取不到主队映射")
                                    # # elif "team" in country_data and Visitors in country_data["team"]:
                                    # if Away_:
                                        # try:
                                            # en_Visitors = country_data["team"][Visitors]
                                            # print("客队英文名称", en_Visitors)
                                            # en_Visitors_ = True
                                            # # 暂时去掉 切换英文 的开关
                                            # Team_mapping = True
                                        # except Exception as e:
                                            # # print(f"{Visitors} 获取不到客队映射")
                                            # pass

                # time.sleep(1)
                # # 设置 Chrome 用户配置文件目录
                # # # 在此之后的操作将会使用保存在用户配置文件中的 Cookie 和登录状态
                # # driver.get("https://sportsbet.io/zh/sports/soccer/inplay")
                # # # 加载保存的 JSON 格式的 Cookie 文件
                # # # with open("D:\project\RecommendationSystem\data\cookies.json", "r") as file:
                # # #     cookies = json.load(file)
                # # # # 循环添加 Cookie
                # # # for cookie in cookies:
                # # #     driver.add_cookie(cookie)
                # # # 进行人机验证的操作（假设选择器是 #challenge-stage > div > label > input[type=checkbox]）
                # # xpath = "/html/body/table/tbody/tr/td/div/div[1]/table/tbody/tr/td[1]/div[1]/div/label/input"
                # # # challenge_checkbox = WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.CSS_SELECTOR, "#challenge-stage > div > label > input[type=checkbox]")))
                # # # challenge_checkbox = WebDriverWait(driver, 1500).until(EC.presence_of_element_located((By.XPATH, xpath)))
                # # # challenge_checkbox = WebDriverWait(driver, 20).until(EC.visibility_of_element_located((By.XPATH, xpath)))
                # # # challenge_checkbox = WebDriverWait(driver, 20).until(EC.element_to_be_clickable((By.XPATH, xpath)))
                # # # 切换到 iframe
                # # iframe = WebDriverWait(driver, 30).until(
                # #     EC.presence_of_element_located((By.XPATH, "/html/body/div[1]/div/div[1]/div/div/iframe"))
                # # )
                # # # iframe = driver.find_element(By.XPATH, "/html/body/div[1]/div/div[1]/div/div/iframe")
                # # #                                       /html/body/div[1]/div/div[1]/div/div/iframe
                # #                                         /html/body/div[1]/div/div[2]/div[1]/div/div[4]/form/fieldset/ul/div[2]/div/div/iframe
                # # driver.switch_to.frame(iframe)
                # # # 定位并点击人机验证的复选框
                # # try:
                # #     challenge_checkbox = WebDriverWait(driver, 30).until(
                # #         EC.presence_of_element_located((By.XPATH, "//input[@type='checkbox']"))
                # #     )
                # #     print("Successfully located challenge checkbox:", challenge_checkbox)
                # #     # challenge_checkbox.click()
                # #     driver.execute_script("arguments[0].click();", challenge_checkbox)
                # #     print("已经加载出来了复选按钮")
                # #     # 切回默认内容
                # #     driver.switch_to.default_content()
                # #     # 等待页面刷新完成
                # #     # WebDriverWait(driver, 50).until(EC.staleness_of(challenge_checkbox))
                # #     # time.sleep(10)
                # # # cookies = driver.get_cookies()
                # # # print("cookies", cookies)
                # # #     driver.refresh()
                # # #     WebDriverWait(driver, 30).until(
                # # #         EC.presence_of_all_elements_located((By.XPATH, "//input[@type='checkbox']")))
                # # # 获取 cookies
                # #     time.sleep(3)
                # #     cookies = driver.get_cookies()
                # #     print("cookies", cookies)
                # #     # # 在后续操作中使用这些 Cookies
                # #     # for cookie in cookies:
                # #     #     driver.add_cookie(cookie)
                # #     # 刷新页面或进行其他操作，以保证 Cookies 生效
                # #     # time.sleep(10)
                # #     # driver.refresh()
                # #     startTime = time.time()
                # #     while True:
                # #         if time.time() - startTime >5:
                # #             break
                # #         print("等待继续")
                # #         cookies = driver.get_cookies()
                # #         time.sleep(1)
                # #     driver.get("https://sportsbet.io/zh/sports/soccer/inplay")
                # #
                # # except TimeoutException as e:
                # #     print("Timeout Exception:", e)
                # # finally:
                # #     # 切回默认内容
                # #     driver.switch_to.default_content()
            # # ------------------------------------------------------------------------------------------
                # # chrome_options = webdriver.ChromeOptions()
                # # # # 打开 Chrome 浏览器并加载配置
                # # chrome_options.add_argument(
                # #     "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
                # # # chrome_options.add_argument(f"user-agent={user_agent}")
                # # chrome_options.binary_location = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
                # # driver = webdriver.Chrome(options=chrome_options,
                # #                                executable_path=r'C:\project\RecommendationSystem\data\chromedriver.exe')
                # # # 1 | open | /zh/sports |  |
                # # driver.get("https://sportsbet.io/zh/sports")
                # # # 2 | setWindowSize | 1920x1040 |  |
                # # # driver.set_window_size(1920, 1040)
                # # driver.set_window_size(1936, 1056)

                # # # 3 | click | css=.sc-hatQeL:nth-child(2) .category-bar__CategoryIcon-sc-4cedq1-3 |  |
                # # time.sleep(8)
                # # # driver.find_element(By.CSS_SELECTOR,
                # # #                          ".sc-hatQeL:nth-child(2) .category-bar__CategoryIcon-sc-4cedq1-3").click()
                # # # driver.find_element(By.XPATH,
                # # #                          "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]").click()
                # # football_e = False
                # # try:
                # #     football_ = WebDriverWait(driver, 60).until(
                # #         EC.presence_of_element_located((By.XPATH, "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
                # #     )
                # #     football_e = True
                # # except Exception as e:
                # #     print("获取足球元素报错")
                # #
                # # if football_e:
                # #     football_.click()


                # # 4 | mouseOver | css=.active > .category-bar__CategoryIcon-sc-4cedq1-3 |  |
                # # element = driver.find_element(By.CSS_SELECTOR, ".active > .category-bar__CategoryIcon-sc-4cedq1-3")
                # # time.sleep(7)

                # # actions = False
                # # try:
                # #     # element = driver.find_element(By.XPATH,
                # #     #                                    "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]")
                # #     element = WebDriverWait(driver, 60).until(
                # #         EC.presence_of_element_located((By.XPATH,
                # #                                         "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
                # #     )
                # #     actions = True
                # # except Exception as e:
                # #     print("移动鼠标报错了")
                # # if actions:
                # #     actions = ActionChains(driver)
                # #     actions.move_to_element(element).perform()
                # #     # 5 | mouseOut | css=.active > .category-bar__CategoryIcon-sc-4cedq1-3 |  |
                # #     element = driver.find_element(By.CSS_SELECTOR, "body")
                # #     actions = ActionChains(driver)
                # #     actions.move_to_element(element).perform()

                # # 6 | click | linkText=联赛 |  |
                # # time.sleep(4)
                # # driver.find_element(By.LINK_TEXT, "联赛").click()



                # bet_league = False
                # time.sleep(15)
                # try:
                    # # liansai_box = WebDriverWait(driver, 30).until(
                    # #     EC.presence_of_element_located((By.LINK_TEXT, "联赛"))
                    # # )

                    # liansai_box = WebDriverWait(driver, 60).until(
                        # EC.presence_of_element_located((By.XPATH, "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a"))
                    # )
                    # # liansai_box = driver.find_element(By.XPATH,
                    # #                     "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a").click()
                    # bet_league = True
                # except Exception as e:
                    # print("获取联赛选项元素抛异常了")
                    # # driver.refresh()
                    # # time.sleep(16)
                    # # try:
                    # #     liansai_box = WebDriverWait(driver, 60).until(
                    # #         EC.presence_of_element_located((By.XPATH,
                    # #                                         "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a"))
                    # #     )
                    # #     # liansai_box = driver.find_element(By.XPATH,
                    # #     #                     "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div[2]/div/nav/ul/li[5]/a").click()
                    # #     bet_league = True
                    # # except Exception as e:
                    # #     print("刷新后再次尝试 获取联赛选项元素还是抛异常了")
                # if bet_league:
                    # liansai_box.click()
                    # time.sleep(10)
                    # country_type = True
                    # try:
                        # challenge_checkbox = WebDriverWait(driver, 50).until(
                            # EC.presence_of_element_located((By.XPATH,
                                                            # "//*[@id='root']/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div/div/div[4]/div//span/a[text()='%s']" %country))
                        # )
                    # except Exception as e:
                        # print("点击联赛后获取不到国家 页面进行刷新后再尝试")
                        # driver.refresh()
                        # try:
                            # challenge_checkbox = WebDriverWait(driver, 50).until(
                                # EC.presence_of_element_located((By.XPATH,
                                                                # "//*[@id='root']/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div/div/div[4]/div//span/a[text()='%s']" % country))
                            # )
                        # except:
                            # print("获取不到国家----")
                            # country_type = False
                    # if country_type:
                        # country_cl = False
                        # print("Successfully located challenge checkbox:", challenge_checkbox)
                        # try:
                            # challenge_checkbox.click()
                            # country_cl = True
                        # except Exception as e:
                            # print("点击国家报错了，尝试JavaScript来点击元素")
                            # driver.execute_script("arguments[0].click();", challenge_checkbox)
                        # if country_cl:
                            # try:
                                # time.sleep(5)
                                # # 展示第三和第四级别的联赛 如果有的话
                                # # 定位到指定的div
                                # WebDriverWait(driver, 50).until(
                                    # EC.presence_of_element_located((By.XPATH,
                                                                    # "/html/body/div[1]/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div/div/div[4]/div[1]/div/div[2]"))
                                # )
                                # base_div_xpath = '/html/body/div[1]/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div/div/div[4]'
                                # base_div = driver.find_element_by_xpath(base_div_xpath)
                                # # 计算子div的数量
                                # child_divs = base_div.find_elements_by_xpath('./div')
                                # num_child_divs = len(child_divs)

                                # # 如果子div数量大于2
                                # if num_child_divs > 2:
                                    # for i in range(2, min(num_child_divs, 4)):
                                        # # 构建完整的XPath
                                        # full_xpath = f'{base_div_xpath}/div[{i + 1}]/div/div[2]'

                                        # # 定位到要点击的元素
                                        # element_to_click = driver.find_element_by_xpath(full_xpath)
                                        # time.sleep(2)
                                        # # 滚动到该元素的位置
                                        # driver.execute_script("arguments[0].scrollIntoView(true);", element_to_click)

                                        # # 点击元素
                                        # element_to_click.click()
                            # except Exception as e:
                                # print("展开三、四级联赛报错了")
                                # try:
                                    # driver.refresh()
                                    # WebDriverWait(driver, 50).until(
                                        # EC.presence_of_element_located((By.XPATH,
                                                                        # "/html/body/div[1]/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div/div/div[4]/div[1]/div/div[2]"))
                                    # )
                                    # base_div_xpath = '/html/body/div[1]/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div/div/div[4]'
                                    # base_div = driver.find_element_by_xpath(base_div_xpath)
                                    # # 计算子div的数量
                                    # child_divs = base_div.find_elements_by_xpath('./div')
                                    # num_child_divs = len(child_divs)

                                    # # 如果子div数量大于2
                                    # if num_child_divs > 2:
                                        # for i in range(2, min(num_child_divs, 4)):
                                            # # 构建完整的XPath
                                            # full_xpath = f'{base_div_xpath}/div[{i + 1}]/div/div[2]'

                                            # # 定位到要点击的元素
                                            # element_to_click = driver.find_element_by_xpath(full_xpath)
                                            # time.sleep(2)
                                            # # 滚动到该元素的位置
                                            # driver.execute_script("arguments[0].scrollIntoView(true);",
                                                                  # element_to_click)
                                            # # 点击元素
                                            # element_to_click.click()
                                # except Exception as e:
                                    # print("刷新页面后 还是展开三、四级联赛报错了")

                        # time.sleep(3)
                        # # EC.presence_of_element_located((By.LINK_TEXT, "AC米兰" %))
                        # race = True
                        # en_race = True
                        # if Home_:
                            # try:
                                # challenge_checkbox_feiha = WebDriverWait(driver, 30).until(
                                    # EC.presence_of_element_located((By.LINK_TEXT, "%s" %Home_team))
                                # )
                                # order_Team_name[0] = Home_team
                            # except Exception as e:
                                # print("使用中文球队名称获取不到比赛")
                                # race = False
                        # if Away_:
                            # try:
                                # challenge_checkbox_feiha = WebDriverWait(driver, 30).until(
                                    # EC.presence_of_element_located((By.LINK_TEXT, "%s" % Visitors))
                                # )
                                # order_Team_name[0] = Visitors
                            # except Exception as e:
                                # print("使用中文球队名称获取不到比赛")
                                # race = False
                        # if race:
                            # time.sleep(5)
                            # try:
                                # challenge_checkbox_feiha.click()
                            # except Exception as e:
                                # print("点击球队名称报错了，尝试Javascript 来点击")
                                # driver.execute_script("arguments[0].click();", challenge_checkbox_feiha)
                        # else:
                            # if Team_mapping:
                                # try:
                                    # print("将页面切换为英文")
                                    # #  | runScript | window.scrollTo(0,300) |  |
                                    # driver.execute_script("window.scrollTo(0,300)")
                                    # #  | click | css=.mEzlN |  |
                                    # driver.find_element(By.CSS_SELECTOR, ".mEzlN").click()
                                    # #  | select | css=.mEzlN | label=English |
                                    # dropdown = driver.find_element(By.CSS_SELECTOR, ".mEzlN")
                                    # dropdown.find_element(By.XPATH, "//option[. = 'English']").click()
                                    # team_en_ = True
                                # except Exception as a:
                                    # print("将页面切换为英文时报了异常")
                                # if Home_:
                                    # if en_home_:
                                        # try:
                                            # challenge_checkbox_feiha_ = WebDriverWait(driver, 30).until(
                                                # EC.presence_of_element_located((By.LINK_TEXT, "%s" % en_Home_team))
                                            # )
                                            # order_Team_name[0] = en_Home_team
                                        # except Exception as e:
                                            # print("使用英文球队也获取不到比赛")
                                            # en_race = False
                                    # else:
                                        # print("获取不到主队映射")
                                # if Away_:
                                    # if en_Visitors_:
                                        # try:
                                            # challenge_checkbox_feiha_ = WebDriverWait(driver, 30).until(
                                                # EC.presence_of_element_located((By.LINK_TEXT, "%s" % en_Visitors))
                                            # )
                                            # order_Team_name[0] = en_Visitors
                                        # except Exception as e:
                                            # print("使用英文球队也获取不到比赛")
                                            # en_race = False
                                    # else:
                                        # print("获取不到客队映射")
                                # if en_race:
                                    # # challenge_checkbox_feiha_.click()
                                    # time.sleep(5)
                                    # try:
                                        # challenge_checkbox_feiha_.click()
                                    # except Exception as e:
                                        # print("点击球队名称报错了，尝试Javascript 来点击")
                                        # driver.execute_script("arguments[0].click();", challenge_checkbox_feiha_)
                            # else:
                                # bet_order = False
                        # time.sleep(8)
                        # if order_Team_name[0]:
                            # matching_elements = []
                            # # 使用XPath获取所有候选元素
                            # team_name_odd = False
                            # try:
                                # elements1 = WebDriverWait(driver, 300).until(
                                    # EC.presence_of_all_elements_located((By.XPATH,
                                                                         # "//*[@id='root']/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div[2]/div/div/div/div[1]/div/div/div/div//div/ul/li//button/div/span/span[contains(text(), '%s (')]"% order_Team_name[0]))
                                # )
                                # team_name_odd = True
                            # except Exception as e:
                                # print("在准备下单时候失去盘口了")

                            # #                                        //*[@id="root"]/div[1]/div[1]/div/div[2]/main/div/div/div/div/div/div/div[2]/div/div/div/div[1]/div/div/div/div[4]/div/ul/li[1]/button/div/span/span[1]
                            # # elements2 = WebDriverWait(self.driver, 30).until(
                            # #     EC.presence_of_all_elements_located((By.XPATH, original_xpath2))
                            # # )
                            # # elements1 = self.driver.find_elements_by_xpath(original_xpath1)
                            # # elements2 = self.driver.find_elements_by_xpath(original_xpath2)
                            # if team_name_odd:
                                # print("elements1", len(elements1))
                                # # # 假设您已经找到了一个符合条件的 element1
                                # # for element1 in elements1:
                                # #     # 检查 element1 是否满足您的条件，如果满足，执行以下代码
                                # #     parent_element = element1.find_element(By.XPATH, "./..")  # 定位到父元素
                                # #     element2 = parent_element.find_element(By.XPATH, "./span[2]/span")  # 从父元素出发找到 element2
                                # for element1 in elements1:
                                    # try:
                                        # text1 = element1.text
                                        # # 从 element1 出发，通过相对路径找到与之对应的 element2
                                        # print("让球信息是：", text1)
                                        # element2 = element1.find_element_by_xpath("./../span[2]/span[1]")
                                        # text2 = element2.text
                                        # print("赔率是：", text2)
                                        # condition1 = False
                                        # if f"{order_Team_name[0]} (" in text1:
                                            # bracket_content = text1.split(f"{order_Team_name[0]} (")[1].split(")")[0]
                                            # if value[4] == 1:
                                                # if "/" in bracket_content:
                                                    # left_value = bracket_content.split("/")[0]
                                                    # if left_value[0] == '-':
                                                        # left_value = left_value[1:]
                                                        # if float(left_value) == 0.5:
                                                            # condition1 = True
                                                            # print("满足条件的 Handicap", float(left_value))
                                                    # if left_value[0] == '+':
                                                        # left_value = left_value[1:]
                                                        # if float(left_value) >= 0:
                                                            # condition1 = True
                                                            # print("满足条件的 Handicap", float(left_value))
                                                # else:
                                                    # print("bracket_content: ", bracket_content)
                                                    # if float(bracket_content) >= -1.0:
                                                        # condition1 = True
                                                        # print("满足条件的 Handicap不带斜杠", bracket_content)
                                                        # print("满足条件的 Handicap不带斜杠", float(bracket_content))
                                            # elif value[4] == 0:
                                                # if "/" in bracket_content:
                                                    # left_value = bracket_content.split("/")[0]
                                                    # if left_value[0] == '+':
                                                        # left_value = left_value[1:]
                                                    # if float(left_value) >= 0:
                                                        # condition1 = True
                                                        # print("满足条件的 Handicap", float(left_value))
                                                # else:
                                                    # if float(bracket_content) >= 0:
                                                        # condition1 = True
                                                        # print("满足条件的 Handicap不带斜杠", bracket_content)
                                                        # print("满足条件的 Handicap不带斜杠", float(bracket_content))
                                        # # 检查第二个条件
                                        # condition2 = False
                                        # if float(text2) >= 1.70:
                                            # condition2 = True
                                            # print("满足条件的todd ", float(text2))
                                            # # 如果两个条件都满足，保存元素
                                        # if condition1 and condition2:
                                            # # print(f"Both conditions are met for element at index {}.")
                                            # matching_elements.append(element1)
                                            # print("两个条件都满足的Handicap", text1, text2)
                                    # except NoSuchElementException:
                                        # print("未找到与当前 element1 关联的 element2")
                                        # continue  # 跳过这一轮循环，继续下一个 element1
                                # if matching_elements:
                                    # print("matching_elements", matching_elements)
                                    # element_book = matching_elements[0]
                                    # try:
                                        # element_book.click()
                                    # except Exception as e:
                                        # print("点击可以下单的赔率和让球元素报错了")
                                    # time.sleep(2)
                                    # # 6 | click | xpath=(//input[@value=''])[2] |  |
                                    # # driver.find_element(By.XPATH, "(//input[@value=''])[2]").click()
                                    # # //*[@id="root"]/div/div[2]/div/div/div/div/ul/li/div/div[4]/div/input
                                    # try:
                                        # man_ = WebDriverWait(driver, 30).until(
                                            # EC.presence_of_all_elements_located((By.XPATH, "//div[@id='root']/div/div[2]/div/div/div/div/ul/li/div/div[4]/div/input"))
                                        # )
                                        # # print(man_)
                                        # man_[0].click()
                                    # except Exception as e:
                                        # print("点击金额输入框时候跑了异常")
                                    # time.sleep(1)
                                    # # 7 | type | xpath=//input[@value='10'] | 10 |
                                    # # driver.find_element(By.XPATH, "//input[@value='10']").send_keys("10")
                                    # try:
                                        # num_ = WebDriverWait(driver, 30).until(
                                            # EC.presence_of_all_elements_located((By.XPATH, "//div[@id='root']/div/div[2]/div/div/div/div/ul/li/div/div[4]/div/input"))
                                        # )
                                        # # print(num_)c'x'd'x
                                        # num_[0].send_keys("100")
                                    # except Exception as e:
                                        # print("设置下单金额抛异常了")
                                    # # 8 | click | xpath=//div[@id='root']/div/div[2]/div/div/div/div[2]/div/ul/li/button/div/span |  |
                                    # # time.sleep(2)
                                    # # driver.find_element(By.XPATH,
                                    # #                          "//div[@id='root']/div/div[2]/div/div/div/div[2]/div/ul/li/button/div/span").click()
                                    # time.sleep(3)
                                    # try:
                                        # bu_ = WebDriverWait(driver, 30).until(
                                            # # EC.presence_of_all_elements_located((By.XPATH, "//div[2]/div/div/div/div[2]/div/ul/li/button/div/span"))
                                            # EC.presence_of_all_elements_located((By.XPATH, "//div[2]/div/div/div/div[2]/div/ul/li/button/div"))
                                        # )
                                        # # print(bu_)
                                        # # bu_[0].click()
                                        # bu_[0].click()
                                        # print(f"完成下单点击了 {Home_and_away}")
                                    # except Exception as e:
                                        # print("加载下单按钮或者点击下单按钮抛异常了")

                                    # check_ = False
                                    # try:
                                        # time.sleep(9)
                                        # test_a = WebDriverWait(driver, 6).until(
                                             # # EC.presence_of_all_elements_located((By.XPATH, "//div[2]/div/div/div/div[2]/div/ul/li/button/div/span"))
                                             # EC.presence_of_all_elements_located(
                                                 # (By.XPATH, "//div[2]/div/div/div/div[2]/div/ul/li/button/div")))
                                        # check_ = True
                                        # # test_a = driver.find_element_by_xpath("//div[2]/div/div/div/div[2]/div/ul/li/button/div")
                                    # except Exception as e:
                                        # print("通过再次获取 下单按钮时候报错了,有可能是一次就下注成功了")
                                    # if check_:
                                        # # ["接受新的赔率", "Accept new odds", "下注", "Place bet"]
                                        # # if test_a[0].text == "接受新的赔率" or test_a[0].text == "Accept new odds" or test_a[0].text == "下注" or test_a[0].text == "Place bet":
                                        # if test_a[0].text in ["接受新的赔率", "Accept new odds", "下注", "Place bet"]:
                                            # try:
                                                # test_a[0].click()
                                                # print(f"点击下单后发生了赔率更新，再次点击下单按钮 {Home_and_away}")
                                                # time.sleep(3)
                                            # except:
                                                # print("再次点击下单按钮报错了")
                                        # else:
                                            # print("再次获取到的下注按钮文案不匹配实际的内容是：", test_a[0].text)
                                    # chek_agen = False
                                    # try:
                                        # time.sleep(3)
                                        # test_a1 = WebDriverWait(driver, 6).until(
                                            # EC.presence_of_all_elements_located(
                                                # (By.XPATH, "//div[2]/div/div/div/div[2]/div/ul/li/button/div")))
                                        # chek_agen = True
                                    # except Exception as e:
                                        # print("第三次再次获取 下单按钮时候报错了,有可能是下注成功了")

                                    # if chek_agen:
                                        # # ["接受新的赔率", "Accept new odds", "下注", "Place bet"]
                                        # if test_a1[0].text in ["接受新的赔率", "Accept new odds", "下注", "Place bet"]:
                                            # try:
                                                # test_a1[0].click()
                                                # print(f"第三次点击下单后发生了赔率更新，再次点击下单按钮 {Home_and_away}")
                                                # time.sleep(3)
                                            # except:
                                                # print("第三次再次点击下单按钮报错了")
                                        # else:
                                            # try:
                                                # print("第三次再次获取到的下注按钮文案不匹配实际的内容是：", test_a1[0].text)
                                            # except Exception as e:
                                                # print("第三次再次获取到的下注按钮文案不匹配实际报错了")
                                                # print(e)

                                    # # 9 | click | xpath=//div[@id='root']/div/div[2]/div/div/div/div[2]/ul/li[3]/div |  |
                                    # # time.sleep(3)
                                    # # driver.find_element(By.XPATH,
                                    # #                          "//div[@id='root']/div/div[2]/div/div/div/div[2]/ul/li[3]/div").click()
                                    # time.sleep(7)
                                    # cl_cc = False
                                    # try:
                                        # cl_ = WebDriverWait(driver, 30).until(
                                            # EC.presence_of_all_elements_located((By.XPATH, "//div[@id='root']/div/div[2]/div/div/div/div[2]/ul/li[3]/div"))
                                        # #                                                   //div[@id='root']/div/div[2]/div/div/div/div[2]/ul/li[3]/div"
                                        # )
                                        # cl_cc = True
                                    # except Exception as e:
                                        # print("第一次没有加载到 关闭订单的元素")
                                        # try:
                                            # cl_ = WebDriverWait(driver, 30).until(
                                                # EC.presence_of_all_elements_located((By.XPATH,
                                                                                     # "//div[@id='root']/div/div[2]/div/div/div/div[2]/ul/li[3]"))
                                            # )
                                            # cl_cc = True
                                        # except Exception as e:
                                            # print("加载到 上级关闭订单的元素还是没有成功")
                                    # # cl_[0].click()
                                    # if cl_cc:
                                        # send_e = False
                                        # try:
                                            # time.sleep(3)
                                            # cl_[0].click()
                                        # except Exception as e:
                                            # time.sleep(4)
                                            # print("点击关闭订单失败了再试一次")
                                            # cl1_ = WebDriverWait(driver, 30).until(
                                                # EC.presence_of_all_elements_located((By.XPATH,
                                                                                     # "//div[@id='root']/div/div[2]/div/div/div/div[2]/ul/li[3]")))
                                            # cl1_[0].click()
                                            # print("没有异常就表示关闭成功了")
                                            # time.sleep(4)
                                        # # _element = driver.find_element(By.CSS_SELECTOR, ".iNasMS > .sc-fFeiMQ")
                                        # # actions = ActionChains(driver)
                                        # # actions.move_to_element(_element).perform()
                                        # # ielement = driver.find_element(By.CSS_SELECTOR, "body")
                                        # # actions = ActionChains(driver)
                                        # # actions.move_to_element(ielement).perform()
                                        # # driver.find_element(By.CSS_SELECTOR,
                                        # #                          ".FullReceiptFooterStyles__CloseButton-sc-1yjga0o-0").click()
                                    # if team_en_:
                                        # try:
                                            # print("将页面切换为中文")
                                            # #  | runScript | window.scrollTo(0,300) |  |
                                            # driver.execute_script("window.scrollTo(0,300)")
                                            # #  | click | css=.mEzlN |  |
                                            # driver.find_element(By.CSS_SELECTOR, ".mEzlN").click()
                                            # #  | select | css=.mEzlN | label=English |
                                            # dropdown = driver.find_element(By.CSS_SELECTOR, ".mEzlN")
                                            # dropdown.find_element(By.XPATH, "//option[. = '中文']").click()
                                            # team_en_ = False
                                        # except Exception as e:
                                            # print("将页面切回到中文时候报了异常")

                                # # time.sleep(3)
                                # time.sleep(2)
                                # # chrome.close_tab(tab)
                                # # 开始处理下单， 根据data 的数据进行下单
                                # # print("完成了一次现金下单")
                                # # driver.close()
                                # try:
                                    # driver.find_element(By.XPATH,
                                                        # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/ol/li/a/span").click()
                                    # print(f"完成了一次下单的逻辑重新回到足球页面 {Home_and_away}")
                                # except Exception as e:
                                    # print("回到足球页面时候 抛异常了")
                                # time.sleep(2)

                        # if team_en_:
                            # try:
                                # print("将页面切换为中文")
                                # #  | runScript | window.scrollTo(0,300) |  |
                                # driver.execute_script("window.scrollTo(0,300)")
                                # #  | click | css=.mEzlN |  |
                                # driver.find_element(By.CSS_SELECTOR, ".mEzlN").click()
                                # #  | select | css=.mEzlN | label=English |
                                # dropdown = driver.find_element(By.CSS_SELECTOR, ".mEzlN")
                                # dropdown.find_element(By.XPATH, "//option[. = '中文']").click()
                                # team_en_ = False
                            # except Exception as e:
                                # print("将页面切回到中文时候报了异常")

                # time.sleep(5)
                # football_e_ = False
                # try:
                    # football_b = WebDriverWait(driver, 60).until(
                        # EC.presence_of_element_located((By.XPATH,
                                                        # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
                    # )
                    # football_e_ = True
                # except Exception as e:
                    # print("获取足球元素报错b")
                # if football_e_:
                    # football_b.click()
                    # print("回到足球页面")

                # actions_b = False
                # try:
                    # # element = driver.find_element(By.XPATH,
                    # #                                    "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]")
                    # element_b = WebDriverWait(driver, 60).until(
                        # EC.presence_of_element_located((By.XPATH,
                                                        # "//div[@id='root']/div/div/div/div[2]/main/div/div/div/div/div/div/div/div/div/div/div/ul/li[2]/a/div[2]"))
                    # )
                    # actions_b = True
                # except Exception as e:
                    # print("移动鼠标报错了")
                # if actions_b:
                    # actions_b = ActionChains(driver)
                    # actions_b.move_to_element(element_b).perform()
                    # # 5 | mouseOut | css=.active > .category-bar__CategoryIcon-sc-4cedq1-3 |  |
                    # element_b = driver.find_element(By.CSS_SELECTOR, "body")
                    # actions_b = ActionChains(driver)
                    # actions_b.move_to_element(element_b).perform()

                # # 如果列表中包含非字符串类型的元素，比如整数或浮点数，你需要先将它们转换为字符串，然后再连接
                # # 使用列表推导式和str()方法转换所有非字符串类型的元素
                # if send_e:
                    # list_of_strings = [str(item) for item in value]
                    # # 现在我们可以连接列表中的字符串了
                    # string_of_items = ' '.join(list_of_strings)
                    # Base.send_email(title="需要处理", content=string_of_items)
                    # send_e = True

                # # driver.quit()
                # # time.sleep(15)
        # driver.quit()
        # time.sleep(15)

    def process_tuple(self, game_d):
        is_Handicap = False
        is_odds = False
        is_Score = False
        is_order = False
        is_finish = False
        Home_name = game_d[2].split(' VS ')[0]
        list_game_d = list(game_d)
        list_game_d.append("n")
        if '主队积分高于客队' in game_d[2]:
            # result_data = Driver_data(self.driver_path).List_driver(game_d[3])
            start_time = time.time()  # 记录开始时间
            dirve_quit = False
            send = False
            while not self.stop_event.is_set():
                current_time = time.time()  # 获取当前时间
                elapsed_time = current_time - start_time  # 计算已经过去的时间
                if elapsed_time > 16 * 60 and not send:
                    # 如果需要避免后续对 list_game_d 的修改影响到队列中的对象，您可以考虑使用 copy.deepcopy() 来放入队列的是 list_game_d 的一个深拷贝版本，而不是原始引用。
                    # self.results_q.put(list_game_d)
                    send = True
                elif elapsed_time > 90 * 60:  # 判断是否超过 85 分钟
                    break  # 如果超过，退出循环
                elif is_Handicap and is_odds and is_Score:
                    print("条件满足可以开始下单")
                    is_order = True
                    self.results_q.put(list_game_d)
                    break

                print(f"{Home_name} 主队积分高 开始监控数据的逻辑")
                is_Handicap = False
                is_odds = False
                is_Score = False
                # driver.set_page_load_timeout(60)
                retry_count = 0
                max_retries = 3
                dirve_quit = False
                checkbox = None
                driver = webdriver.Chrome(executable_path=self.driver_path)
                driver.set_page_load_timeout(120)
                while retry_count < max_retries:
                    try:
                        driver.get(game_d[3].replace("Analysis", "shijian"))
                        break  # 如果页面成功加载，跳出循环
                    except TimeoutException:
                        retry_count += 1
                        print(f"Timeout occurred. Retry {retry_count} of {max_retries}.")
                        driver.refresh()  # 刷新页面
                if retry_count == max_retries:
                    print(f"重试三次加载页面失败Failed to load the page after {max_retries} attempts.")
                time.sleep(2)
                try:
                    checkbox = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                    )
                except Exception as e:
                    print("没有获取到广告图片关闭按钮")
                if checkbox:
                    try:
                        checkbox.click()
                        print("关闭了广告")
                    except Exception as e:
                        time.sleep(5)
                        checkbox_ = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                        )
                        try:
                            checkbox_.click()
                            print("再次尝试点击关闭广告")
                        except Exception as e:
                            time.sleep(5)
                            checkbox__ = WebDriverWait(driver, 20).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                            )
                            try:
                                checkbox__.click()
                                print("第三次尝试点击关闭广告")
                            except Exception as e:
                                print("第三次尝试关闭广告失败")

                time.sleep(1)
                Rounds_ = False
                # 根据直播页面中是否存在 '让球指数'来判断 让球数据是否可用加载
                try:
                    Rounds = WebDriverWait(driver, 30).until(
                        EC.presence_of_element_located(
                            (By.XPATH, '/html/body/div[2]/div[1]/div/div[1]'))
                    )
                    if Rounds.text:
                        Rounds_ = True
                except Exception as e:
                    print("获取直播页面数据抛异常了")
                if Rounds_:
                    print(f"{Home_name} 获取到了直播页面数据")
                else:
                    print("获取直播页面数据失败")
                    dirve_quit = True
                    break
                # 根据比赛状态是否 不为 推迟和待定来判断是否继续
                state = False
                try:
                    states = WebDriverWait(driver, 30).until(
                        EC.presence_of_element_located(
                            (By.XPATH, '/html/body/div[1]/div/div/div/div[3]/div[2]/div/div[2]')))
                    state = True
                except Exception as e:
                    print(f"{Home_name} 第一次获取不到比赛状态")
                    try:
                        states = WebDriverWait(driver, 30).until(
                            EC.presence_of_element_located(
                                (By.XPATH, '/html/body/div[1]/div/div[1]/div/div[3]/div[2]')))
                        state = True
                    except Exception as e:
                        print(f"{Home_name} 重试后依然获取不到比赛状态")
                if state:
                    if states.text in ["推迟", "待定", "完场"]:
                        dirve_quit = True
                        break

                # self.driver.find_element(By.CSS_SELECTOR, "body > div > img").click()
                # 一些项目为了反爬就必须用行为链才actions 可以操作
                # actions = ActionChains(driver)

                # submitBtn = driver.find_element_by_id("menu1")
                # # submitBtn = driver.find_element_by_class_name("btn")  # 先找到它
                # # 需要一个行为链,把鼠标移到到这个上面
                # # actions.move_to_element(submitBtn)
                # # 然后再点击
                # # actions.click(submitBtn)
                # # 点击之后还没有完 还要执行才可以
                # # actions.perform()
                # # 为了防止操作太快网页还没有来的及加载就开始下面的selenium 操作 那不行 需要让程序睡眠等待一下 再继续跑
                # # time.sleep(1)
                # submitBtn.click()
                # time.sleep(5)
                data = driver.page_source
                # driver.close()
                driver.quit()
                xpath_data = etree.HTML(data)
                # result_list = xpath_data.xpath('/html/body/div[2]/div[2]/span[@class="filterBtn2 on"]/text()')
                # 如果用 bs4 就用下面这个
                # soup = BeautifulSoup(result_data, 'lxml')
                # result = soup.select('ul[id="todaySchedule"] li')
                time.sleep(5)
                # 即使存在'让球指数' 也不意味着 一定会有让球数据
                try:
                    handicap_data = xpath_data.xpath('/html/body/div[2]/div[1]/table/tbody/tr[last()]/td[4]/span[2]/text()')[0]
                    print(f"主队 {Home_name} 让球数是： ", handicap_data)
                except Exception as e:
                    print(f"主队 {Home_name} 在获取赔率和让球数据时候抛出了异常")
                    #  在这里需要
                    print("获取下单的让球数据报了异常")
                    print(e)
                    break

                # if is_Handicap and is_odds:
                if handicap_data:
                    homeScore = None
                    guestScore = None
                    try:
                        homeScore = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div/div[1]/text()')[0]
                        guestScore = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div/div[3]/text()')[0]
                        print(f"主队 {Home_name} 目前比分 {homeScore} : {guestScore}")
                    except Exception as e:
                        print(f"主队 {Home_name} 第一次没有获取到主客场比分")
                        # time.sleep(2)
                        # break
                        try:
                            homeScore = \
                            xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[1]/text()')[0]
                            guestScore = \
                            xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[3]/text()')[0]
                            print(f"主队 {Home_name} 目前比分 {homeScore} : {guestScore}")
                        except Exception as e:
                            print(f"主队 {Home_name} 第二次没有获取到主客场比分")
                    if homeScore and guestScore:
                        if homeScore < guestScore:
                            is_Score = True
                            list_game_d[4] = 1
                        elif homeScore == guestScore:
                            is_Score = True
                            list_game_d[4] = 0
                        else:
                            # is_Score = False
                            print(f"{Home_name} 目前强队已经比分领先了")

                if handicap_data:
                    if list_game_d[4] == 1:
                        if "/" in handicap_data:
                            left_data = handicap_data.split('/')[0]
                            try:
                                s = float(left_data)
                                if s < 1:
                                    is_Handicap = True
                            except ValueError:
                                print("left_data的值是什么？", left_data)

                        else:
                            try:
                                print(f"{Home_name} handicap_data", handicap_data)
                                s = float(handicap_data)
                                if s <= 1:
                                    is_Handicap = True
                            except ValueError:
                                print("handicap_data 的值是什么？", handicap_data)

                    if list_game_d[4] == 0:
                        if "/" in handicap_data:
                            left_data = handicap_data.split('/')[0]
                            try:
                                s = float(left_data)
                                if s < 0.5:
                                    is_Handicap = True
                            except ValueError:
                                print("left_data的值是什么？", left_data)

                        else:
                            try:
                                print(f"{Home_name} handicap_data", handicap_data)
                                s = float(handicap_data)
                                if s < 0.5:
                                    is_Handicap = True
                            except ValueError:
                                print("handicap_data 的值是什么？", handicap_data)

                else:
                    print(f"{Home_name}目前的没有看到让球数据")
                if is_Handicap:
                    try:
                        odds = xpath_data.xpath('/html/body/div[2]/div[1]/table/tbody/tr[last()]/td[4]/span[1]/text()')[0]
                    except ValueError:
                        print(f"{Home_name}获取不到赔率数据")
                        break
                    if odds:
                        try:
                            odds_ = float(odds)
                            print("odds:", odds)
                            print("odds_:", odds_)
                        except ValueError:
                            odds_ = odds
                        if odds_ >= 0.67:
                            is_odds = True
                    else:
                        print("目前没有看到赔率数据")
                time.sleep(15)
            if dirve_quit:
                driver.quit()

            if is_order:
                print("已经下单，开始进入监控比赛结果")
                start_time_t = time.time()  # 记录开始时间
                while True:
                    current_time_t = time.time()  # 获取当前时间
                    elapsed_time_t = current_time_t - start_time_t  # 计算已经过去的时间
                    if elapsed_time_t > 100 * 75:  # 判断是否超过 85 分钟
                        break  # 如果超过，退出循环
                    # elif
                    if is_finish:
                        break
                    # 判断下单结果是赢还是输
                    print(f"{Home_name} 开始下单后监控输赢结果的逻辑")
                    # driver.set_page_load_timeout(60)
                    retry_count = 0
                    max_retries = 3
                    checkbox = None
                    driver = webdriver.Chrome(executable_path=self.driver_path)
                    driver.set_page_load_timeout(100)
                    while retry_count < max_retries:
                        try:
                            print("比赛进行中的数据", game_d[2], game_d[3].replace("Analysis", "shijian"))
                            driver.get(game_d[3].replace("Analysis", "shijian"))
                            break  # 如果页面成功加载，跳出循环
                        except TimeoutException:
                            retry_count += 1
                            print(f"Timeout occurred. Retry {retry_count} of {max_retries}.")
                            driver.refresh()  # 刷新页面
                    if retry_count == max_retries:
                        print(f"重试三次加载页面失败Failed to load the page after {max_retries} attempts.")
                        time.sleep(2)
                        try:
                            checkbox = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                            )
                        except Exception as e:
                            print("没有获取到广告图片关闭按钮")
                    if checkbox:
                        try:
                            checkbox.click()
                            print("关闭了广告")
                        except Exception as e:
                            time.sleep(5)
                            checkbox_ = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                            )
                            try:
                                checkbox_.click()
                                print("再次尝试点击关闭广告")
                            except Exception as e:
                                time.sleep(5)
                                checkbox__ = WebDriverWait(driver, 20).until(
                                    EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                                )
                                try:
                                    checkbox__.click()
                                    print("第三次尝试点击关闭广告")
                                except Exception as e:
                                    print("第三次尝试关闭广告失败")
                    time.sleep(1)
                    Rounds_ = False
                    try:
                        Rounds = WebDriverWait(driver, 30).until(
                            EC.presence_of_element_located(
                                (By.XPATH, '/html/body/div[2]/div[1]/div/div[1]'))
                        )
                        if Rounds.text:
                            Rounds_ = True
                    except Exception as e:
                        print(f"{Home_name} 获取直播页面数据抛异常了")
                    if Rounds_:
                        print(f"{Home_name} 获取到了直播页面数据")
                    else:
                        print("获取直播页面数据失败")
                        driver.quit()
                        break
                    time.sleep(1)
                    data = driver.page_source
                    # driver.close()
                    driver.quit()
                    time.sleep(1)
                    xpath_data = etree.HTML(data)
                    Competing_ = False
                    try:
                        Competing_time = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[2]/text()')
                        if not Competing_time:
                            Competing_time = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[2]/span/text()')
                            print(f"{Home_name} 下单后监控比赛状态是", Competing_time)
                            Competing_ = True
                        else:
                            print(f"{Home_name} 下单后监控比赛状态是", Competing_time)
                            Competing_ = True
                    except ValueError:
                        print(f"{Home_name} 下单后监控目前没有获取到比赛的状态")

                    if Competing_ and Competing_time:
                        # ['完场', '推迟', '待定']
                        if Competing_time[0] == '完场':
                            try:
                                homeScore = xpath_data.xpath(
                                    '/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div/div[1]/text()')[0]
                                guestScore = xpath_data.xpath(
                                    '/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div/div[3]/text()')[0]
                            except Exception as e:
                                print(f"{Home_name} 下单后监控没有获取到主客场比分")
                                break
                            if homeScore and guestScore:
                                if homeScore <= guestScore:
                                    self.bet_result_q.put(False)
                                else:
                                    self.bet_result_q.put(True)
                                is_finish = True
                    time.sleep(600)

                # return
            # else:
            #     print("比赛数据不完整或者超过了时间 无法判断是否可以操作")
            #     return
        else:
            print("客队积分比较高")
            # sleep_duration = 44 * 60  # 将分钟数转换为秒数
            # time.sleep(sleep_duration)
            # driver = webdriver.Chrome(executable_path=self.driver_path)

            # time_s = 0
            # while time_s < 6:
            #     current_time = datetime.now()
            #     print(game_d[2],current_time)
            #     driver = webdriver.Chrome(executable_path=self.driver_path)
            #     driver.set_page_load_timeout(60)
            #     try:
            #         driver.get(game_d[3].replace("Analysis", "shijian"))
            #     except Exception as e:
            #         print(game_d[2],"打开客队领先的 分析比赛页面失败了")
            #         pass
            #     time.sleep(20)
            #     # driver.close()
            #     driver.quit()
            #     time.sleep(265)
            #     time_s += 1

            # now = datetime.now()
            # game_time = self.get_game_time(game[1], now)
            # print("game_time:", game_time)
            # # 获取当前时间
            # current_time = datetime.now()
            # # 计算时间差
            # time_difference = current_time - game_time
            # # 将时间差转换为分钟数
            # time_difference_in_minutes = time_difference.total_seconds() / 60
            # # 判断是否超过100分钟
            # if time_difference_in_minutes > 100:
            #     print("当前时间超过了给定时间100分钟。")

            #
            now_ = datetime.now()
            game_time_ = self.get_game_time(game_d[1], now_)
            current_time_ = datetime.now()
            # 计算时差
            time_difference_ = current_time_ - game_time_
            # 如果时差小于30分钟, 每间隔5分钟执行一次保活逻辑
            if time_difference_ < timedelta(minutes=22):
                while time_difference_ < timedelta(minutes=22):
                    print(game_d[2], current_time_)
                    driver = webdriver.Chrome(executable_path=self.driver_path)
                    driver.set_page_load_timeout(60)
                    try:
                        driver.get(game_d[3].replace("Analysis", "shijian"))
                    except Exception as e:
                        print(game_d[2],"打开客队领先的 分析比赛页面失败了")
                        pass
                    time.sleep(20)
                    # driver.close()
                    driver.quit()
                    time.sleep(5 * 50)
                    current_time_ = datetime.now()
                    time_difference_ = current_time_ - game_time_


            start_time = time.time()  # 记录开始时间
            dirve_quit = False
            # 直接将results_q 放入队列 会出现奇怪的问题，建议使用深层副本 将拷贝的值放入队列 copy.deepcopy() 来放入队列的是 list_game_d 的一个深拷贝版本，而不是原始引用。
            # self.results_q.put(list_game_d)
            while not self.stop_event.is_set():

                # current_time = time.time()  # 获取当前时间
                # elapsed_time = current_time - start_time  # 计算已经过去的时间
                # if elapsed_time > 63 * 60:  # 判断是否超过 85 分钟
                #     break  # 如果超过，退出循环
                gnow_ = datetime.now()
                ggame_time_ = self.get_game_time(game_d[1], gnow_)
                gcurrent_time_ = datetime.now()
                gtime_difference_ = gcurrent_time_ - ggame_time_
                # 如果时差小于30分钟, 每间隔5分钟执行一次保活逻辑
                if gtime_difference_ > timedelta(minutes=90):
                    print("比赛已经距离开始超过90分钟 不进行监控")
                    break  # 如果超过，退出循环
                elif is_Handicap and is_odds and is_Score:
                    print("条件满足可以开始下单")
                    is_order = True
                    self.results_q.put(list_game_d)
                    break
                print(f"{Home_name} 客队积分高 开始监控数据的逻辑")
                print("list_game_d", list_game_d)
                is_Handicap = False
                is_odds = False
                is_Score = False
                retry_count = 0
                max_retries = 3
                checkbox = None
                dirve_quit = False
                driver = webdriver.Chrome(executable_path=self.driver_path)
                driver.set_page_load_timeout(100)
                while retry_count < max_retries:
                    try:
                        driver.get(game_d[3].replace("Analysis", "shijian"))
                        break  # 如果页面成功加载，跳出循环
                    except Exception as e:
                        retry_count += 1
                        print(f"Timeout occurred. Retry {retry_count} of {max_retries}.")
                        driver.refresh()  # 刷新页面
                if retry_count == max_retries:
                    print(f"重试三次加载页面失败Failed to load the page after {max_retries} attempts.")
                    time.sleep(2)
                    try:
                        checkbox = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                        )
                    except Exception as e:
                        print("没有获取到广告图片关闭按钮")
                if checkbox:
                    try:
                        checkbox.click()
                        print("关闭了广告")
                    except Exception as e:
                        time.sleep(5)
                        checkbox_ = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                        )
                        try:
                            checkbox_.click()
                            print("再次尝试点击关闭广告")
                        except Exception as e:
                            time.sleep(5)
                            checkbox__ = WebDriverWait(driver, 20).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                            )
                            try:
                                checkbox__.click()
                                print("第三次尝试点击关闭广告")
                            except Exception as e:
                                print("第三次尝试关闭广告失败")
                time.sleep(1)
                Rounds_ = False

                try:
                    Rounds = WebDriverWait(driver, 30).until(
                        EC.presence_of_element_located(
                            (By.XPATH, '/html/body/div[2]/div[1]/div/div[1]'))
                    )
                    if Rounds.text:
                        Rounds_ = True
                except Exception as e:
                    print("获取直播页面数据抛异常了")
                if Rounds_:
                    print(f"{Home_name} 获取到了直播页面数据")
                else:
                    print("获取直播页面数据失败")
                    dirve_quit = True
                    break

                # 根据比赛状态是否 不为 推迟和待定来判断是否继续
                state1 = False
                try:
                    states1 = WebDriverWait(driver, 30).until(
                        EC.presence_of_element_located(
                            (By.XPATH, '/html/body/div[1]/div/div/div/div[3]/div[2]/div/div[2]')))
                    state1 = True
                except Exception as e:
                    print(f"{Home_name} 第一次获取不到比赛状态")
                    try:
                        states1 = WebDriverWait(driver, 30).until(
                            EC.presence_of_element_located(
                                (By.XPATH, '/html/body/div[1]/div/div[1]/div/div[3]/div[2]')))
                        state1 = True
                    except Exception as e:
                        print(f"{Home_name} 重试后依然获取不到比赛状态")

                if state1:
                    if states1.text in ["推迟", "待定", "完场"]:
                        dirve_quit = True
                        break
                time.sleep(1)
                data = driver.page_source
                # driver.close()
                driver.quit()
                time.sleep(1)
                xpath_data = etree.HTML(data)
                time.sleep(5)
                # 即使可以获取到 让球指数 单位，也不一定其中会有让球数据
                try:
                    handicap_data = xpath_data.xpath('/html/body/div[2]/div[1]/table/tbody/tr[last()]/td[4]/span[2]/text()')[0]
                    print(f"主队{Home_name}让球数是： ", handicap_data)
                except Exception as e:
                    print(f"主队 {Home_name} 在获取赔率和让球数据时候抛出了异常")
                    break
                    #  在这里需要
                    print(e)

                # if is_Handicap and is_odds:
                if handicap_data:
                    homeScore = None
                    guestScore = None
                    try:
                        homeScore = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div/div[1]/text()')[0]
                        # print(f"{Home_name} homeScore__", homeScore)
                        guestScore = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div/div[3]/text()')[0]
                        # print("guestScore__", guestScore)
                        print(f"主队 {Home_name} 目前比分 {homeScore} : {guestScore}")
                    except Exception as e:
                        print(f"主队 {Home_name} 第一次没有获取到主客场比分")
                        # time.sleep(2)
                        # break
                        try:
                            homeScore = \
                            xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[1]/text()')[0]
                            # print(f"{Home_name} homeScore__", homeScore)
                            guestScore = \
                            xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[3]/text()')[0]
                            # print("guestScore__", guestScore)
                            print(f"主队 {Home_name} 目前比分 {homeScore} : {guestScore}")
                        except Exception as e:
                            print(f"主队 {Home_name} 第二次没有获取到主客场比分")
                    if homeScore and guestScore:
                        if homeScore > guestScore:
                            is_Score = True
                            list_game_d[4] = 1
                        elif homeScore == guestScore:
                            is_Score = True
                            list_game_d[4] = 0
                        else:
                            print(f"{Home_name} 目前强队已经比分领先了 客队是强队")

                if handicap_data:
                    if list_game_d[4] == 1:
                        if "/" in handicap_data:
                            left_data = handicap_data.split('/')[0]
                            try:
                                s = float(left_data)
                                if s >= -0.5:
                                    is_Handicap = True
                            except ValueError:
                                # if s >= 0:
                                #     is_Handicap = True
                                print("left_data是", left_data)
                        else:
                            try:
                                s = float(handicap_data)
                                if s >= -0.5:
                                    is_Handicap = True
                            except ValueError:
                                # if s >= 0:
                                #     is_Handicap = True
                                print("handicap_data是", handicap_data)
                    elif list_game_d[4] == 0:
                        if "/" in handicap_data:
                            left_data = handicap_data.split('/')[0]
                            try:
                                s = float(left_data)
                                if s >= 0:
                                    is_Handicap = True
                            except ValueError:
                                # if s >= 0:
                                #     is_Handicap = True
                                print("left_data是", left_data)
                        else:
                            try:
                                s = float(handicap_data)
                                if s >= 0:
                                    is_Handicap = True
                            except ValueError:
                                # if s >= 0:
                                #     is_Handicap = True
                                print("handicap_data是", handicap_data)
                else:
                    print("目前的没有看到让球数据")
                if is_Handicap:
                    try:
                        odds = xpath_data.xpath('/html/body/div[2]/div[1]/table/tbody/tr[last()]/td[4]/span[3]/text()')[0]
                    except ValueError:
                        print("获取不到赔率数据")
                        break
                    if odds:
                        try:
                            print("odds", odds)
                            odds_ = float(odds)
                        except Exception as e:
                            odds_ = odds
                            print("odds_", odds_)
                        if isinstance(odds_, int) or isinstance(odds_, float):
                            if odds_ >= 0.67:
                                is_odds = True
                        else:
                            print("获取到的odds_ 数据有问题")
                    else:
                        print("目前没有看到赔率数据")

                time.sleep(15)
            if dirve_quit:
                driver.quit()

            if is_order:
                print("已经开始下单了")
                start_time_t = time.time()  # 记录开始时间
                while True:
                    current_time_t = time.time()  # 获取当前时间
                    elapsed_time_t = current_time_t - start_time_t  # 计算已经过去的时间
                    if elapsed_time_t > 100 * 65:  # 判断是否超过 85 分钟
                        break  # 如果超过，退出循环

                    # egnow_ = datetime.now()
                    # eggame_time_ = self.get_game_time(game_d[1], gnow_)
                    # egcurrent_time_ = datetime.now()
                    # egtime_difference_ = gcurrent_time_ - ggame_time_
                    # # 如果时差小于30分钟, 每间隔5分钟执行一次保活逻辑
                    # if egtime_difference_ > timedelta(minutes=125):
                    #     print("比赛已经距离开始超过85分钟 不进行监控")
                    #     break  # 如果超过，退出循环

                    if is_finish:
                        break
                    print(f"{Home_name} 客队积分高  开始下单后的输赢结果开监控数据的逻辑")
                    driver = webdriver.Chrome(executable_path=self.driver_path)
                    driver.set_page_load_timeout(100)
                    retry_count = 0
                    max_retries = 3
                    checkbox = None
                    while retry_count < max_retries:
                        try:
                            driver.get(game_d[3].replace("Analysis", "shijian"))
                            break  # 如果页面成功加载，跳出循环
                        except TimeoutException:
                            retry_count += 1
                            print(f"Timeout occurred. Retry {retry_count} of {max_retries}.")
                            driver.refresh()  # 刷新页面
                    if retry_count == max_retries:
                        print(f"重试三次加载页面失败Failed to load the page after {max_retries} attempts.")
                        time.sleep(2)
                        try:
                            checkbox = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                            )
                        except Exception as e:
                            print("没有获取到广告图片关闭按钮")
                    if checkbox:
                        try:
                            checkbox.click()
                            print("关闭了广告")
                        except Exception as e:
                            time.sleep(5)
                            checkbox_ = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                            )
                            try:
                                checkbox_.click()
                                print("再次尝试点击关闭广告")
                            except Exception as e:
                                time.sleep(5)
                                checkbox__ = WebDriverWait(driver, 20).until(
                                    EC.presence_of_element_located((By.CSS_SELECTOR, "body > div > img"))
                                )
                                try:
                                    checkbox__.click()
                                    print("第三次尝试点击关闭广告")
                                except Exception as e:
                                    print("第三次尝试关闭广告失败")

                    time.sleep(1)
                    Rounds_ = False
                    try:
                        Rounds = WebDriverWait(driver, 30).until(
                            EC.presence_of_element_located(
                                (By.XPATH, '/html/body/div[2]/div[1]/div/div[1]'))
                        )
                        if Rounds.text:
                            Rounds_ = True
                    except Exception as e:
                        print("获取直播页面数据抛异常了")
                    if Rounds_:
                        print(f"{Home_name} 获取到了直播页面数据")
                    else:
                        print("获取直播页面数据失败")
                        driver.quit()
                        break
                    time.sleep(1)
                    data = driver.page_source
                    # driver.close()
                    driver.quit()
                    # 判断下单结果是赢还是输
                    time.sleep(1)
                    xpath_data = etree.HTML(data)
                    Competing_ = False
                    try:
                        Competing_time = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[2]/text()')
                        if not Competing_time:
                            Competing_time = xpath_data.xpath('/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[2]/span/text()')
                            print(f"{Home_name} 下单后监控比赛状态", Competing_time)
                            Competing_ = True
                        else:
                            print(f"{Home_name} 下单后监控比赛状态", Competing_time)
                            Competing_ = True
                    except ValueError:
                        print(f"{Home_name} 下单后监控目前没有获取到比赛的状态")

                    if Competing_:
                        if Competing_time:
                            if Competing_time[0] == '完场':
                                try:
                                    homeScore = xpath_data.xpath(
                                        '/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[1]/text()')[0]
                                    guestScore = xpath_data.xpath(
                                        '/html/body/div[1]/div/div[1]/div/div[3]/div[2]/div/div[3]/text()')[0]
                                except ValueError:
                                    print(f"{Home_name} 下单后监控没有获取到主客场比分")
                                    break
                                if homeScore and guestScore:
                                    if homeScore >= guestScore:
                                        self.bet_result_q.put(False)
                                    else:
                                        self.bet_result_q.put(True)
                                    is_finish = True
                    time.sleep(600)
    def process_url(self, game, is_last=False):
        # 创建一个线程终止事件
        if is_last:
            print("All URLs have been processed!")
            # self.stop_threads = True
            # for t in self.threads:
            #     t.join()
            # # 清空线程列表
            # self.threads = []
            time.sleep(6000)
            print("今天到此为止")
            self.stop_event.set()
            self.results_q.put(None)
            self.bet_result_q.put(None)
        else:
            if game == "Start Signal":
                print("Start signal received.")
                bet_thread = threading.Thread(target=self.read_results_q)
                bet_thread.start()
                # stop_event.set()  # 设置终止事件
                # # 等待处理线程完成
                # bet_thread.join()
                bet_stpp = threading.Thread(target=self.bet_stop_q)
                bet_stpp.start()
            elif game:  # Only print the URL if it is not empty
                print(f"Processing URL: {game}")
                now = datetime.now()
                game_time = self.get_game_time(game[1], now)
                print("game_time:", game_time)
                # 获取当前时间
                current_time = datetime.now()
                # 计算时间差
                time_difference = current_time - game_time
                # 将时间差转换为分钟数
                time_difference_in_minutes = time_difference.total_seconds() / 60
                # 判断是否超过100分钟
                if time_difference_in_minutes > 100:
                    print("当前时间超过了给定时间100分钟。")
                else:
                    print("当前时间未超过给定时间100分钟。")
                    game_t = threading.Thread(target=self.process_tuple, args=(game,))
                    game_t.start()
                    self.threads.append(game_t)
    def get_game_time(self, game_time_str, now):
        try:
            game_time = datetime.strptime(game_time_str, '%m-%d %H:%M')
            game_time = game_time.replace(year=now.year)
        except ValueError:
            print(f"Failed to parse game time: {game_time_str}")
            game_time = now
        return game_time
    def shutdown_scheduler(self):
        with self.shutdown_lock:
            if self.scheduler.state and self.scheduler.get_jobs():
                self.scheduler.shutdown(wait=False)
    def check_games(self, output_tuples):
        now = datetime.now()
        sorted_games = sorted(output_tuples, key=lambda x: x[1])
        run_dates = []
        for i, game in enumerate(sorted_games):
            game_time = self.get_game_time(game[1], now)
            # print(game_time)
            if game[3] not in self.scheduled_urls:
                self.scheduled_urls.add(game[3])
                delay = timedelta(seconds=random.randint(0, 15))
                if game_time <= now:
                    self.past_game_offset += timedelta(seconds=1)
                    run_date = now + self.past_game_offset
                else:
                    run_date = game_time + delay
                is_last = False  # We will not mark any real URL as the last one
                self.scheduler.add_job(self.process_url, 'date', run_date=run_date, args=[game, is_last])
                run_dates.append(run_date)

                if not self.start_signal_sent:  # Send start signal after the first URL
                    self.scheduler.add_job(self.process_url, 'date', run_date=run_date - timedelta(seconds=1),
                                           args=["Start Signal", False])
                    self.start_signal_sent = True

        if run_dates:
            last_url_time = max(run_dates)
            final_task_time = last_url_time + timedelta(minutes=1)  # The final task will run 1 minute after the last URL
            self.scheduler.add_job(self.process_url, 'date', run_date=final_task_time, args=["", True])  # The final task does not have a real URL

            if not self.start_signal_sent:  # Check if start signal has been sent
                self.scheduler.add_job(self.process_url, 'date', run_date=final_task_time - timedelta(seconds=1),
                                       args=["Start Signal", False])  # Send start signal
                self.start_signal_sent = True

            stop_time = final_task_time + timedelta(seconds=1)
            self.scheduler.add_job(self.shutdown_scheduler, 'date', run_date=stop_time)
        next_check_time = now + timedelta(minutes=1)
        self.scheduler.add_job(self.check_games, 'date', run_date=next_check_time, args=[output_tuples],
                               id='check_games_job', replace_existing=True)

    def run(self, output_tuples):
        self.scheduler.add_job(self.check_games, 'date', run_date=datetime.now(), args=[output_tuples],
                               id='check_games_job')
        self.scheduler.start()
        while self.scheduler.state:
            time.sleep(1)

    def read_status(self,file_path):
        """从 JSON 文件中读取状态值"""
        with open(file_path, 'r') as file:
            data = json.load(file)
            return data.get('status')

    def write_status(self, file_path, status):
        """将状态值写回 JSON 文件"""
        with open(file_path, 'w') as file:
            json.dump({'status': status}, file)

if __name__ == '__main__':
    game_re = Game_Recommendation()
    #
    Todays_game = game_re.game_Details()
    print("今日推荐的比赛是: ", Todays_game)
    Base.send_email(rec="tome",title="今日球赛推荐", content=Todays_game)
    time.sleep(600)

    # output_tuples = [('罗甲', '08-05 10:40', '布加勒斯特迪纳摩 VS CS卡拉奥华大学',
    #                   'https://m.titan007.com/analy/Analysis/2408643.htm'),
    #                  ('俄甲', '08-05 10:45', 'FK秋明 VS 希姆基 主队积分高于客队',
    #                   'https://m.titan007.com/analy/Analysis/2425901.htm'),
    #                  ('智利甲', '08-05 23:38', '帕莱斯蒂诺 VS 科金博',
    #                   'https://m.titan007.com/analy/Analysis/2324456.htm'),
    #                  ('巴西乙', '08-05 22:53', '保地花高SP VS 克里丘马',
    #                   'https://m.titan007.com/analy/Analysis/2352881.htm'),
    #                  ('阿乙B', '08-05 21:46', '阿特兰大竞技 VS 迈普',
    #                   'https://m.titan007.com/analy/Analysis/2343394.htm')]

    #
    # # # 获取文件的最后修改时间
    import datetime as dt
    file_time = dt.datetime.fromtimestamp(os.path.getmtime(os.path.join(Conf.get_data_excel_path(),ConfigYmal().get_gamelist_file())))
    # # 获取当前时间
    now = dt.datetime.now()
    # 比较文件的最后修改时间与当前时间之间的差异
    time_difference = now - file_time
    if time_difference <= dt.timedelta(hours=4):
        output_tuples = game_re.read_file()
        print("今天的候选赛事： ", output_tuples)
        game_re.run(output_tuples)
    else:
        print("没有今日新的赛事文件")

    # output_tuples = game_re.read_file()
    # print("今天的候选赛事： ",output_tuples)
    # game_re.run(output_tuples)

    # game_re.read_results_q()
