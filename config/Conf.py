import sys
sys.path.append(r"C:\Jenkins_workspaces\workspace\RecommendedSystem")
import os
from utils.YmalUtils import YamlReader
from selenium import webdriver

# 获取当前项目的绝对路径
current = os.path.abspath(__file__)
BASE_DIR = os.path.dirname(os.path.dirname(current))

# 定义config 目录路径，路径拼接建议使用os.sep 这样可以不受环境影响
_config_path = BASE_DIR + os.sep + "config"
# 定义conf.yaml 文件路径
_config_file_yaml = _config_path + os.sep + "conf.yml"
# 定义data 目录路径
_data_path = BASE_DIR + os.sep + "data"
# 定义 driver.exe 程序路径
driver_path = _data_path + os.sep + "chromedriver.exe"
# 定义 gamelist_file 文件路径

# 定义excel文件路径 方法
def get_data_excel_path():
    return _data_path

#定义一个方法来返回conf.yaml 文件路径 访问外部使用
def get_config_file_yaml():
    return _config_file_yaml
# 定义一个方法来返回
def get_driver():
    # print(driver_path)
    return driver_path

# 读取配置文件

class ConfigYmal:
    def __init__(self):
        self.config = YamlReader(_config_file_yaml).data()

    #定义方法来获取 配置文件中需要的具体信息的value 比如文件名称
    def get_excel_file(self):
        # 读到的信息和yaml 文件中的格式一样，是字典类型
        return self.config["BASE"]["test"]["case_file"]
    def get_excel_sheet(self):
        return self.config["BASE"]["test"]["case_sheet"]
    def get_start_url(self):
        return self.config["BASE"]["test"]["start_url"]
    def get_url_Prefix(self):
        return self.config["BASE"]["test"]["url_Prefix"]
    def get_url_Suffix(self):
        return self.config["BASE"]["test"]["url_Suffix"]
    def get_email_info(self):
        """
        获取邮件配置相关信息
        :return:
        """
        return self.config["email"]
    def get_gamelist_file(self):
        return self.config["BASE"]["test"]["gamelist_file"]
    def Internationalization_mapping(self):
        return self.config["BASE"]["test"]["Internationalization_mapping"]
    def Internationalization_mapping_(self):
        return self.config["BASE"]["test"]["Internationalization_mapping_"]
    def get_signal_file(self):
        return self.config["BASE"]["test"]["signal_file"]

if __name__ == '__main__':
    conf_read = ConfigYmal()
    print(get_config_file_yaml())
    # print(conf_read.get_excel_file())
    # print(conf_read.get_excel_sheet())
    print(get_driver())
    # print(conf_read.get_email_info())

