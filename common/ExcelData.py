import sys
sys.path.append(r"C:\Jenkins_workspaces\workspace\RecommendedSystem")
from utils.ExcelUtil import ExcelReader
from common.ExcelConfig import DataConfig

class Data:
    def __init__(self,excel_file,excel_sheet):
        self.redata = ExcelReader(excel_file,excel_sheet)
        # 所有联赛
        self._League_data = list()
        # 所有洲际杯赛
        self._Club_Intercontinental_Cup = list()
        # 所有国家队比赛
        self._Country_games = list()
        # 表头
        self.datakey = DataConfig()
    def get_Filter_events(self):
        for result in self.redata.data():
            self._League_data.append(result[self.datakey.League_name])
        for result5 in self.redata.data():
            self._Club_Intercontinental_Cup.append(result5[self.datakey.Club_Intercontinental_Cup])
        for result6 in self.redata.data():
            self._Country_games.append(result6[self.datakey.Country_games])
        # print(self._League_data,self._Club_Intercontinental_Cup,self._Country_games)
        return self._League_data,self._Club_Intercontinental_Cup,self._Country_games

    def get_case_pre(self,pre):
        for line in self.redata.data():
            if pre in dict(line).values():
                return line
        return None



        #
        # 列表推导式

if __name__ == '__main__':
    Data("../data/LeagueOverview.xlsx","PushRules").get_Filter_events()