import sys
sys.path.append(r"C:\Jenkins_workspaces\workspace\RecommendedSystem")
#                 C:\Jenkins_workspaces\workspace\RecommendedSystem
from utils.EmailUtil import SendEmail
from config.Conf import ConfigYmal

# 发邮件的公共方法
def send_email(rec="",report_html_path="",content="",title="测试"):
    email_info = ConfigYmal().get_email_info()
    # 初始化类
    # email服务器地址
    smtp_addr = email_info["smtpserver"]
    username = email_info["username"]
    password = email_info["password"]
    # 接收者
    recv = email_info["receiver"]
    # title 随便写一个
    email = SendEmail(
        smtp_addr =smtp_addr,
        username=username,
        password =password,
        rec=rec,
        recv=recv,
        title=title,
        content=content,
        file=report_html_path
    )
    email.send_mail()

if __name__ == '__main__':
    send_email(title="今日球赛推荐",content="英超")
